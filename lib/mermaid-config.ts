import mermaid from 'mermaid';

export const lightThemeConfig = {
  theme: 'default',
  themeVariables: {
    // 基础颜色 - 使用新的设计系统
    primaryColor: '#8B7355', // --primary
    primaryTextColor: '#3C3A35', // --foreground
    primaryBorderColor: '#C4BFB6', // --border
    lineColor: '#8B8680', // --muted-foreground
    sectionBkgColor: '#F5F4F2', // --muted
    altSectionBkgColor: '#FEFEFE', // --card
    gridColor: '#C4BFB6', // --border
    secondaryColor: '#E8E5E0', // --secondary
    tertiaryColor: '#FEFEFE', // --card
    // 甘特图专用配置
    cScale0: '#8B7355',
    cScale1: '#7A9B76',
    cScale2: '#B8860B',
    cScale3: '#CD5C5C',
    cScale4: '#9370DB',
    cScale5: '#4682B4',
    taskBkgColor: '#8B7355',
    taskTextColor: '#FFFFFF',
    taskTextLightColor: '#3C3A35',
    taskTextOutsideColor: '#3C3A35',
    taskTextClickableColor: '#3C3A35',
    activeTaskBkgColor: '#7A9B76',
    activeTaskBorderColor: '#6B8E6B',
    gridColor: '#C4BFB6',
    section0: '#F5F4F2',
    section1: '#E8E5E0',
    section2: '#C4BFB6',
    section3: '#A8A29E',
    todayLineColor: '#CD5C5C',
    // 旅程图配置
    journeyLabelColor: '#3C3A35',
    journeyLabelBackground: '#FEFEFE',
    journeyLabelBorder: '#C4BFB6',
    journeyTaskBackground: '#8B7355',
    journeyTaskBorder: '#7A6B4F',
    journeyTaskTextColor: '#FFFFFF',
    journeySectionBackground: '#F5F4F2',
    journeySectionBorder: '#C4BFB6',
  },
};

export const darkThemeConfig = {
  theme: 'dark',
  themeVariables: {
    // 基础颜色 - 使用新的设计系统暗色主题
    primaryColor: '#B5A082', // --primary (dark)
    primaryTextColor: '#F5F4F2', // --foreground (dark)
    primaryBorderColor: '#5A5651', // --border (dark)
    lineColor: '#C4BFB6', // --muted-foreground (dark)
    sectionBkgColor: '#4A453F', // --card (dark)
    altSectionBkgColor: '#3C3731', // --background (dark)
    gridColor: '#5A5651', // --border (dark)
    secondaryColor: '#5A5651', // --secondary (dark)
    tertiaryColor: '#4A453F', // --card (dark)
    // 甘特图专用配置
    cScale0: '#B5A082',
    cScale1: '#9BB894',
    cScale2: '#D4AF37',
    cScale3: '#F08080',
    cScale4: '#DDA0DD',
    cScale5: '#87CEEB',
    taskBkgColor: '#B5A082',
    taskTextColor: '#FFFFFF',
    taskTextLightColor: '#F5F4F2',
    taskTextOutsideColor: '#F5F4F2',
    taskTextClickableColor: '#F5F4F2',
    activeTaskBkgColor: '#9BB894',
    activeTaskBorderColor: '#8AA883',
    gridColor: '#6B7280',
    section0: '#5A5651',
    section1: '#6B7280',
    section2: '#9CA3AF',
    section3: '#C4BFB6',
    todayLineColor: '#F08080',
    // 旅程图配置
    journeyLabelColor: '#F5F4F2',
    journeyLabelBackground: '#5A5651',
    journeyLabelBorder: '#6B7280',
    journeyTaskBackground: '#B5A082',
    journeyTaskBorder: '#A69175',
    journeyTaskTextColor: '#FFFFFF',
    journeySectionBackground: '#4A453F',
    journeySectionBorder: '#5A5651',
  },
};

export const baseMermaidConfig = {
  startOnLoad: false,
  securityLevel: 'loose' as const,
  fontFamily: 'Libre Baskerville, serif',
  fontSize: 14,
  htmlLabels: true,
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true,
    curve: 'basis',
  },
  sequence: {
    useMaxWidth: true,
    diagramMarginX: 50,
    diagramMarginY: 10,
    actorMargin: 50,
    width: 150,
    height: 65,
    boxMargin: 10,
    boxTextMargin: 5,
    noteMargin: 10,
    messageMargin: 35,
  },
  gantt: {
    useMaxWidth: true,
    leftPadding: 120,
    gridLineStartPadding: 35,
    fontSize: 14,
    sectionFontSize: 16,
    numberSectionStyles: 4,
    axisFormat: '%m/%d',
    tickInterval: '1day',
    weekday: 'monday',
    bottomPadding: 25,
    rightPadding: 75,
    topPadding: 75,
  },
  class: {
    useMaxWidth: true,
  },
  state: {
    useMaxWidth: true,
  },
  pie: {
    useMaxWidth: true,
  },
  er: {
    useMaxWidth: true,
  },
  journey: {
    useMaxWidth: true,
    diagramMarginX: 50,
    diagramMarginY: 10,
    leftMargin: 150,
    width: 150,
    height: 50,
    boxMargin: 10,
    boxTextMargin: 5,
    noteMargin: 10,
    messageMargin: 35,
    bottomMarginAdj: 1,
    rightAngles: false,
    taskFontSize: 14,
    taskFontFamily: 'Inter, sans-serif',
    taskMargin: 50,
    activationWidth: 10,
    textPlacement: 'fo',
    actorColours: ['#8FBC8F', '#FFB347', '#87CEEB', '#DDA0DD', '#F0E68C'],
    sectionColours: ['#fff2cc', '#f8cecc', '#e1d5e7', '#dae8fc', '#d5e8d4'],
  },
};

export const initializeMermaid = (isDark: boolean = false) => {
  const themeConfig = isDark ? darkThemeConfig : lightThemeConfig;
  
  mermaid.initialize({
    ...baseMermaidConfig,
    ...themeConfig,
  });
};

export const renderMermaid = async (
  element: HTMLElement,
  code: string,
  id: string = 'mermaid-diagram'
): Promise<{ svg: string; bindFunctions?: any }> => {
  try {
    // 清除之前的内容
    element.innerHTML = '';
    
    // 渲染新的图表
    const { svg, bindFunctions } = await mermaid.render(id, code);
    element.innerHTML = svg;
    
    // 如果有绑定函数，执行它们
    if (bindFunctions) {
      bindFunctions(element);
    }
    
    return { svg, bindFunctions };
  } catch (error) {
    console.error('Mermaid rendering error:', error);
    element.innerHTML = `
      <div class="flex items-center justify-center h-full text-red-500 bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
        <div class="text-center">
          <div class="text-lg font-semibold mb-2">图表渲染错误</div>
          <div class="text-sm opacity-75">${error instanceof Error ? error.message : '未知错误'}</div>
        </div>
      </div>
    `;
    throw error;
  }
};

export const validateMermaidSyntax = (code: string): { isValid: boolean; error?: string } => {
  try {
    // 简单的语法验证
    if (!code.trim()) {
      return { isValid: false, error: '代码不能为空' };
    }
    
    // 检查是否包含基本的图表类型关键词
    const diagramTypes = [
      'flowchart', 'graph', 'sequenceDiagram', 'classDiagram', 
      'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie'
    ];
    
    const hasValidType = diagramTypes.some(type => 
      code.toLowerCase().includes(type.toLowerCase())
    );
    
    if (!hasValidType) {
      return { 
        isValid: false, 
        error: '请确保代码包含有效的图表类型（如 flowchart, sequenceDiagram 等）' 
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : '语法验证失败' 
    };
  }
};
