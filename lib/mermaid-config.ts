import mermaid from 'mermaid';

export const lightThemeConfig = {
  theme: 'default',
  themeVariables: {
    primaryColor: '#3b82f6',
    primaryTextColor: '#1f2937',
    primaryBorderColor: '#e5e7eb',
    lineColor: '#6b7280',
    sectionBkgColor: '#f9fafb',
    altSectionBkgColor: '#ffffff',
    gridColor: '#e5e7eb',
    secondaryColor: '#f3f4f6',
    tertiaryColor: '#ffffff',
    // 甘特图专用配置
    cScale0: '#3b82f6',
    cScale1: '#10b981',
    cScale2: '#f59e0b',
    cScale3: '#ef4444',
    cScale4: '#8b5cf6',
    cScale5: '#06b6d4',
    taskBkgColor: '#3b82f6',
    taskTextColor: '#ffffff',
    taskTextLightColor: '#1f2937',
    taskTextOutsideColor: '#1f2937',
    taskTextClickableColor: '#1f2937',
    activeTaskBkgColor: '#10b981',
    activeTaskBorderColor: '#059669',
    gridColor: '#e5e7eb',
    section0: '#f9fafb',
    section1: '#f3f4f6',
    section2: '#e5e7eb',
    section3: '#d1d5db',
    todayLineColor: '#ef4444',
  },
};

export const darkThemeConfig = {
  theme: 'dark',
  themeVariables: {
    primaryColor: '#3b82f6',
    primaryTextColor: '#f9fafb',
    primaryBorderColor: '#374151',
    lineColor: '#9ca3af',
    sectionBkgColor: '#1f2937',
    altSectionBkgColor: '#111827',
    gridColor: '#374151',
    secondaryColor: '#374151',
    tertiaryColor: '#1f2937',
    // 甘特图专用配置
    cScale0: '#60a5fa',
    cScale1: '#34d399',
    cScale2: '#fbbf24',
    cScale3: '#f87171',
    cScale4: '#a78bfa',
    cScale5: '#22d3ee',
    taskBkgColor: '#3b82f6',
    taskTextColor: '#ffffff',
    taskTextLightColor: '#f9fafb',
    taskTextOutsideColor: '#f9fafb',
    taskTextClickableColor: '#f9fafb',
    activeTaskBkgColor: '#10b981',
    activeTaskBorderColor: '#059669',
    gridColor: '#4b5563',
    section0: '#374151',
    section1: '#4b5563',
    section2: '#6b7280',
    section3: '#9ca3af',
    todayLineColor: '#ef4444',
  },
};

export const baseMermaidConfig = {
  startOnLoad: false,
  securityLevel: 'loose' as const,
  fontFamily: 'Inter, ui-sans-serif, system-ui, sans-serif',
  fontSize: 14,
  htmlLabels: true,
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true,
    curve: 'basis',
  },
  sequence: {
    useMaxWidth: true,
    diagramMarginX: 50,
    diagramMarginY: 10,
    actorMargin: 50,
    width: 150,
    height: 65,
    boxMargin: 10,
    boxTextMargin: 5,
    noteMargin: 10,
    messageMargin: 35,
  },
  gantt: {
    useMaxWidth: true,
    leftPadding: 120,
    gridLineStartPadding: 35,
    fontSize: 14,
    sectionFontSize: 16,
    numberSectionStyles: 4,
    axisFormat: '%m/%d',
    tickInterval: '1day',
    weekday: 'monday',
    bottomPadding: 25,
    rightPadding: 75,
    topPadding: 75,
  },
  class: {
    useMaxWidth: true,
  },
  state: {
    useMaxWidth: true,
  },
  pie: {
    useMaxWidth: true,
  },
  er: {
    useMaxWidth: true,
  },
  journey: {
    useMaxWidth: true,
  },
};

export const initializeMermaid = (isDark: boolean = false) => {
  const themeConfig = isDark ? darkThemeConfig : lightThemeConfig;
  
  mermaid.initialize({
    ...baseMermaidConfig,
    ...themeConfig,
  });
};

export const renderMermaid = async (
  element: HTMLElement,
  code: string,
  id: string = 'mermaid-diagram'
): Promise<{ svg: string; bindFunctions?: any }> => {
  try {
    // 清除之前的内容
    element.innerHTML = '';
    
    // 渲染新的图表
    const { svg, bindFunctions } = await mermaid.render(id, code);
    element.innerHTML = svg;
    
    // 如果有绑定函数，执行它们
    if (bindFunctions) {
      bindFunctions(element);
    }
    
    return { svg, bindFunctions };
  } catch (error) {
    console.error('Mermaid rendering error:', error);
    element.innerHTML = `
      <div class="flex items-center justify-center h-full text-red-500 bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
        <div class="text-center">
          <div class="text-lg font-semibold mb-2">图表渲染错误</div>
          <div class="text-sm opacity-75">${error instanceof Error ? error.message : '未知错误'}</div>
        </div>
      </div>
    `;
    throw error;
  }
};

export const validateMermaidSyntax = (code: string): { isValid: boolean; error?: string } => {
  try {
    // 简单的语法验证
    if (!code.trim()) {
      return { isValid: false, error: '代码不能为空' };
    }
    
    // 检查是否包含基本的图表类型关键词
    const diagramTypes = [
      'flowchart', 'graph', 'sequenceDiagram', 'classDiagram', 
      'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie'
    ];
    
    const hasValidType = diagramTypes.some(type => 
      code.toLowerCase().includes(type.toLowerCase())
    );
    
    if (!hasValidType) {
      return { 
        isValid: false, 
        error: '请确保代码包含有效的图表类型（如 flowchart, sequenceDiagram 等）' 
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : '语法验证失败' 
    };
  }
};
