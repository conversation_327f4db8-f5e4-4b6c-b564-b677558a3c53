export interface MermaidExample {
  id: string;
  title: string;
  description: string;
  code: string;
  category: string;
}

export const mermaidExamples: MermaidExample[] = [
  {
    id: 'flowchart-basic',
    title: '基础流程图',
    description: '简单的流程图示例',
    category: '流程图',
    code: `flowchart TD
    A[开始] --> B{是否满足条件?}
    B -->|是| C[执行操作]
    B -->|否| D[跳过操作]
    C --> E[结束]
    D --> E`
  },
  {
    id: 'sequence-basic',
    title: '基础时序图',
    description: '用户登录时序图',
    category: '时序图',
    code: `sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 输入用户名密码
    F->>B: 发送登录请求
    B->>D: 验证用户信息
    D-->>B: 返回验证结果
    B-->>F: 返回登录状态
    F-->>U: 显示登录结果`
  },
  {
    id: 'class-basic',
    title: '基础类图',
    description: '简单的类关系图',
    category: '类图',
    code: `classDiagram
    class Animal {
        +String name
        +int age
        +eat()
        +sleep()
    }
    
    class Dog {
        +String breed
        +bark()
    }
    
    class Cat {
        +String color
        +meow()
    }
    
    Animal <|-- Dog
    Animal <|-- Cat`
  },
  {
    id: 'gantt-basic',
    title: '基础甘特图',
    description: '项目进度甘特图',
    category: '甘特图',
    code: `gantt
    title 项目开发进度
    dateFormat YYYY-MM-DD
    axisFormat %m/%d

    section 设计
    需求分析    :done, task1, 2024-01-01, 2024-01-05
    UI设计     :done, task2, 2024-01-06, 2024-01-12

    section 开发
    前端开发    :active, task3, 2024-01-13, 2024-02-15
    后端开发    :task4, 2024-01-20, 2024-02-20

    section 测试
    单元测试    :task5, 2024-02-16, 2024-02-25
    集成测试    :task6, 2024-02-21, 2024-03-01`
  },
  {
    id: 'gantt-simple',
    title: '简单甘特图',
    description: '字体清晰的简化甘特图',
    category: '甘特图',
    code: `gantt
    title 网站开发
    dateFormat YYYY-MM-DD

    section 阶段一
    设计    :done, a1, 2024-01-01, 2024-01-07
    开发    :active, a2, 2024-01-08, 2024-01-21

    section 阶段二
    测试    :a3, 2024-01-22, 2024-01-28
    上线    :a4, 2024-01-29, 2024-01-31`
  },
  {
    id: 'pie-basic',
    title: '基础饼图',
    description: '数据分布饼图',
    category: '饼图',
    code: `pie title 编程语言使用分布
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "TypeScript" : 15
    "其他" : 5`
  },
  {
    id: 'state-basic',
    title: '基础状态图',
    description: '订单状态流转图',
    category: '状态图',
    code: `stateDiagram-v2
    [*] --> 待支付
    待支付 --> 已支付 : 支付成功
    待支付 --> 已取消 : 取消订单
    已支付 --> 已发货 : 商家发货
    已支付 --> 已退款 : 申请退款
    已发货 --> 已完成 : 确认收货
    已发货 --> 已退款 : 退货退款
    已完成 --> [*]
    已取消 --> [*]
    已退款 --> [*]`
  },
  {
    id: 'er-basic',
    title: '基础ER图',
    description: '数据库实体关系图',
    category: 'ER图',
    code: `erDiagram
    USER {
        int id PK
        string username
        string email
        datetime created_at
    }
    
    POST {
        int id PK
        string title
        text content
        int user_id FK
        datetime created_at
    }
    
    COMMENT {
        int id PK
        text content
        int post_id FK
        int user_id FK
        datetime created_at
    }
    
    USER ||--o{ POST : creates
    POST ||--o{ COMMENT : has
    USER ||--o{ COMMENT : writes`
  },
  {
    id: 'journey-basic',
    title: '基础用户旅程图',
    description: '用户购物旅程',
    category: '旅程图',
    code: `journey
    title 用户购物旅程
    section 发现阶段
      浏览商品: 5: 用户
      搜索产品: 3: 用户
      查看详情: 4: 用户
    section 决策阶段
      比较价格: 2: 用户
      查看评价: 4: 用户
      咨询客服: 3: 用户, 客服
    section 购买阶段
      加入购物车: 5: 用户
      结算支付: 3: 用户
      确认订单: 5: 用户
    section 售后阶段
      物流跟踪: 4: 用户
      确认收货: 5: 用户
      评价商品: 4: 用户`
  }
];

export const getExamplesByCategory = () => {
  const categories = [...new Set(mermaidExamples.map(example => example.category))];
  return categories.reduce((acc, category) => {
    acc[category] = mermaidExamples.filter(example => example.category === category);
    return acc;
  }, {} as Record<string, MermaidExample[]>);
};

export const getExampleById = (id: string) => {
  return mermaidExamples.find(example => example.id === id);
};

export const defaultMermaidCode = `flowchart TD
    A[开始] --> B{是否满足条件?}
    B -->|是| C[执行操作]
    B -->|否| D[跳过操作]
    C --> E[结束]
    D --> E`;
