@import "tailwindcss";

:root {
  --font-inter: 'Inter', sans-serif;
  --font-jetbrains-mono: 'JetBrains Mono', monospace;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  height: 100%;
}

body {
  font-family: var(--font-inter);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Monaco Editor 样式调整 */
.monaco-editor {
  font-family: var(--font-jetbrains-mono) !important;
}

/* Mermaid 图表样式调整 */
.mermaid {
  font-family: var(--font-inter) !important;
}

/* 甘特图字体优化 */
.mermaid .gantt-section-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  fill: currentColor !important;
}

.mermaid .gantt-task-text {
  font-size: 14px !important;
  font-weight: 500 !important;
  fill: white !important;
}

.mermaid .gantt-axis-text {
  font-size: 12px !important;
  font-weight: 400 !important;
  fill: currentColor !important;
}

.mermaid .gantt-today-line {
  stroke-width: 2px !important;
}

/* 确保甘特图在暗色主题下的可读性 */
.dark .mermaid .gantt-section-title {
  fill: #f9fafb !important;
}

.dark .mermaid .gantt-axis-text {
  fill: #d1d5db !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monaco-editor {
    font-size: 12px !important;
  }
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
}
