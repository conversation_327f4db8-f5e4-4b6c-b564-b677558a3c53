@import "tailwindcss";

:root {
  --background: oklch(0.9582 0.0152 90.2357);
  --foreground: oklch(0.3760 0.0225 64.3434);
  --card: oklch(0.9914 0.0098 87.4695);
  --card-foreground: oklch(0.3760 0.0225 64.3434);
  --popover: oklch(0.9914 0.0098 87.4695);
  --popover-foreground: oklch(0.3760 0.0225 64.3434);
  --primary: oklch(0.6180 0.0778 65.5444);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8846 0.0302 85.5655);
  --secondary-foreground: oklch(0.4313 0.0300 64.9288);
  --muted: oklch(0.9239 0.0190 83.0636);
  --muted-foreground: oklch(0.5391 0.0387 71.1655);
  --accent: oklch(0.8348 0.0426 88.8064);
  --accent-foreground: oklch(0.3760 0.0225 64.3434);
  --destructive: oklch(0.5471 0.1438 32.9149);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8606 0.0321 84.5881);
  --input: oklch(0.8606 0.0321 84.5881);
  --ring: oklch(0.6180 0.0778 65.5444);
  --chart-1: oklch(0.6180 0.0778 65.5444);
  --chart-2: oklch(0.5604 0.0624 68.5805);
  --chart-3: oklch(0.4851 0.0570 72.6827);
  --chart-4: oklch(0.6777 0.0624 64.7755);
  --chart-5: oklch(0.7264 0.0581 66.6967);
  --sidebar: oklch(0.9239 0.0190 83.0636);
  --sidebar-foreground: oklch(0.3760 0.0225 64.3434);
  --sidebar-primary: oklch(0.6180 0.0778 65.5444);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8348 0.0426 88.8064);
  --sidebar-accent-foreground: oklch(0.3760 0.0225 64.3434);
  --sidebar-border: oklch(0.8606 0.0321 84.5881);
  --sidebar-ring: oklch(0.6180 0.0778 65.5444);
  --font-sans: Libre Baskerville, serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.25rem;
  --shadow-2xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-sm: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow-md: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12);
  --shadow-lg: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12);
  --shadow-xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12);
  --shadow-2xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.30);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2747 0.0139 57.6523);
  --foreground: oklch(0.9239 0.0190 83.0636);
  --card: oklch(0.3237 0.0155 59.0603);
  --card-foreground: oklch(0.9239 0.0190 83.0636);
  --popover: oklch(0.3237 0.0155 59.0603);
  --popover-foreground: oklch(0.9239 0.0190 83.0636);
  --primary: oklch(0.7264 0.0581 66.6967);
  --primary-foreground: oklch(0.2747 0.0139 57.6523);
  --secondary: oklch(0.3795 0.0181 57.1280);
  --secondary-foreground: oklch(0.9239 0.0190 83.0636);
  --muted: oklch(0.3237 0.0155 59.0603);
  --muted-foreground: oklch(0.7982 0.0243 82.1078);
  --accent: oklch(0.4186 0.0281 56.3404);
  --accent-foreground: oklch(0.9239 0.0190 83.0636);
  --destructive: oklch(0.5471 0.1438 32.9149);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3795 0.0181 57.1280);
  --input: oklch(0.3795 0.0181 57.1280);
  --ring: oklch(0.7264 0.0581 66.6967);
  --chart-1: oklch(0.7264 0.0581 66.6967);
  --chart-2: oklch(0.6777 0.0624 64.7755);
  --chart-3: oklch(0.6180 0.0778 65.5444);
  --chart-4: oklch(0.5604 0.0624 68.5805);
  --chart-5: oklch(0.4851 0.0570 72.6827);
  --sidebar: oklch(0.2747 0.0139 57.6523);
  --sidebar-foreground: oklch(0.9239 0.0190 83.0636);
  --sidebar-primary: oklch(0.7264 0.0581 66.6967);
  --sidebar-primary-foreground: oklch(0.2747 0.0139 57.6523);
  --sidebar-accent: oklch(0.4186 0.0281 56.3404);
  --sidebar-accent-foreground: oklch(0.9239 0.0190 83.0636);
  --sidebar-border: oklch(0.3795 0.0181 57.1280);
  --sidebar-ring: oklch(0.7264 0.0581 66.6967);
  --font-sans: Libre Baskerville, serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.25rem;
  --shadow-2xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-sm: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow-md: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12);
  --shadow-lg: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12);
  --shadow-xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12);
  --shadow-2xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.30);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  height: 100%;
}

body {
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background);
  color: var(--foreground);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted-foreground);
}

/* Monaco Editor 样式调整 */
.monaco-editor {
  font-family: var(--font-mono) !important;
}

/* Mermaid 图表样式调整 */
.mermaid {
  font-family: var(--font-sans) !important;
}

/* 甘特图字体优化 */
.mermaid .gantt-section-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  fill: currentColor !important;
}

.mermaid .gantt-task-text {
  font-size: 14px !important;
  font-weight: 500 !important;
  fill: white !important;
}

.mermaid .gantt-axis-text {
  font-size: 12px !important;
  font-weight: 400 !important;
  fill: currentColor !important;
}

.mermaid .gantt-today-line {
  stroke-width: 2px !important;
}

/* 确保甘特图在暗色主题下的可读性 */
.dark .mermaid .gantt-section-title {
  fill: var(--foreground) !important;
}

.dark .mermaid .gantt-axis-text {
  fill: var(--muted-foreground) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monaco-editor {
    font-size: 12px !important;
  }
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
}
