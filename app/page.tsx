'use client';

import { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { defaultMermaidCode } from '../lib/examples';
import { getCodeFromUrl, saveToLocalStorage, loadFromLocalStorage, STORAGE_KEYS } from '../lib/utils';
import Toolbar from '../components/Toolbar';
import Examples from '../components/Examples';

// 动态导入组件以避免 SSR 问题
const Editor = dynamic(() => import('../components/Editor'), {
  ssr: false,
  loading: () => (
    <div className="h-full flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
  )
});

const Preview = dynamic(() => import('../components/Preview'), {
  ssr: false,
  loading: () => (
    <div className="h-full flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>
  )
});

export default function Home() {
  const [code, setCode] = useState<string>('');
  const [currentSvg, setCurrentSvg] = useState<string>('');
  const [isExamplesOpen, setIsExamplesOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor');
  const previewRef = useRef<HTMLDivElement>(null);

  // 初始化代码
  useEffect(() => {
    const initializeCode = () => {
      // 首先尝试从 URL 获取代码
      const urlCode = getCodeFromUrl();
      if (urlCode) {
        setCode(urlCode);
        return;
      }

      // 然后尝试从本地存储获取
      const savedCode = loadFromLocalStorage(STORAGE_KEYS.LAST_CODE, '');
      if (savedCode) {
        setCode(savedCode);
        return;
      }

      // 最后使用默认代码
      setCode(defaultMermaidCode);
    };

    initializeCode();
    setIsLoading(false);
  }, []);

  // 保存代码到本地存储
  useEffect(() => {
    if (code && !isLoading) {
      saveToLocalStorage(STORAGE_KEYS.LAST_CODE, code);
    }
  }, [code, isLoading]);

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
  };

  const handleRenderComplete = (svg: string) => {
    setCurrentSvg(svg);
  };

  const handleSelectExample = (exampleCode: string) => {
    setCode(exampleCode);
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* 工具栏 */}
      <Toolbar
        code={code}
        onCodeChange={handleCodeChange}
        previewRef={previewRef}
        currentSvg={currentSvg}
      />

      {/* 主要内容区域 - 上下分割 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 编辑器和预览区域 - 上半部分 */}
        <div className="flex-1 min-h-0">
          {/* 桌面端：可调整大小的面板 */}
          <div className="hidden md:block h-full">
            <PanelGroup direction="horizontal" className="h-full">
              {/* 编辑器面板 */}
              <Panel defaultSize={50} minSize={20} maxSize={80}>
                <div className="h-full p-4">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold text-foreground">
                        代码编辑器
                      </h2>
                    </div>
                    <div className="flex-1 min-h-0">
                      <Editor
                        value={code}
                        onChange={handleCodeChange}
                      />
                    </div>
                  </div>
                </div>
              </Panel>

              {/* 分割线 */}
              <PanelResizeHandle className="w-2 bg-gray-200 dark:bg-gray-700 hover:bg-blue-400 dark:hover:bg-blue-600 transition-colors cursor-col-resize relative group">
                <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-gray-400 dark:bg-gray-500 group-hover:bg-blue-500 transition-colors"></div>
              </PanelResizeHandle>

              {/* 预览面板 */}
              <Panel defaultSize={50} minSize={20} maxSize={80}>
                <div className="h-full p-4">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold text-foreground">
                        图表预览
                      </h2>
                      <div className="text-sm text-muted-foreground">
                        实时渲染
                      </div>
                    </div>
                    <div className="flex-1 min-h-0">
                      <div ref={previewRef} className="h-full">
                        <Preview
                          code={code}
                          onRenderComplete={handleRenderComplete}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Panel>
            </PanelGroup>
          </div>

          {/* 移动端：标签页切换 */}
          <div className="md:hidden h-full flex flex-col">
            {/* 标签页头部 */}
            <div className="flex border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
              <button
                onClick={() => setActiveTab('editor')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'editor'
                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                代码编辑器
              </button>
              <button
                onClick={() => setActiveTab('preview')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'preview'
                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                图表预览
              </button>
            </div>

            {/* 标签页内容 */}
            <div className="flex-1 min-h-0">
              {activeTab === 'editor' ? (
                <div className="h-full p-4">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        代码编辑器
                      </h2>
                    </div>
                    <div className="flex-1 min-h-0">
                      <Editor
                        value={code}
                        onChange={handleCodeChange}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="h-full p-4">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        图表预览
                      </h2>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        实时渲染
                      </div>
                    </div>
                    <div className="flex-1 min-h-0">
                      <div ref={previewRef} className="h-full">
                        <Preview
                          code={code}
                          onRenderComplete={handleRenderComplete}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 示例模板区域 - 下半部分 */}
        <div className={`border-t border-gray-200 dark:border-gray-700 transition-all duration-300 ${
          isExamplesOpen ? 'h-80' : 'h-12'
        }`}>
          <div className="h-full flex flex-col">
            {/* 示例模板头部 */}
            <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                示例模板
              </h3>
              <button
                onClick={() => setIsExamplesOpen(!isExamplesOpen)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg transition-colors"
                title={isExamplesOpen ? "收起示例" : "展开示例"}
              >
                <svg
                  className={`w-5 h-5 transition-transform duration-200 ${isExamplesOpen ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              </button>
            </div>

            {/* 示例模板内容 */}
            {isExamplesOpen && (
              <div className="flex-1 overflow-hidden">
                <Examples
                  onSelectExample={handleSelectExample}
                  isOpen={true}
                  onToggle={() => setIsExamplesOpen(false)}
                />
              </div>
            )}
          </div>
        </div>
      </div>


    </div>
  );
}
