{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5901b7c6.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_5901b7c6-module__ec5Qua__className\",\n  \"variable\": \"inter_5901b7c6-module__ec5Qua__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5901b7c6.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_707233f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_707233f-module__9hS9bG__className\",\n  \"variable\": \"jetbrains_mono_707233f-module__9hS9bG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_707233f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-jetbrains-mono%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,mTAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,mTAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"../components/ThemeProvider\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  subsets: [\"latin\"],\n  variable: \"--font-jetbrains-mono\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Mermaid 在线编辑器\",\n  description: \"实时编辑和预览 Mermaid 图表的在线工具\",\n  keywords: [\"mermaid\", \"diagram\", \"flowchart\", \"editor\", \"online\"],\n  authors: [{ name: \"Mermaid Online Editor\" }],\n  viewport: \"width=device-width, initial-scale=1\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\" suppressHydrationWarning>\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"light\"\n          enableSystem={false}\n          disableTransitionOnChange={false}\n        >\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAW;QAAW;QAAa;QAAU;KAAS;IACjE,SAAS;QAAC;YAAE,MAAM;QAAwB;KAAE;IAC5C,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,yRAAC;QAAK,MAAK;QAAQ,wBAAwB;kBACzC,cAAA,yRAAC;YACC,WAAW,GAAG,oJAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,4JAAa,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBAE9E,cAAA,yRAAC,wLAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,cAAc;gBACd,2BAA2B;0BAE1B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,8JACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}