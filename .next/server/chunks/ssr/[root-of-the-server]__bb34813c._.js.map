{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/lib/examples.ts"], "sourcesContent": ["export interface MermaidExample {\n  id: string;\n  title: string;\n  description: string;\n  code: string;\n  category: string;\n}\n\nexport const mermaidExamples: MermaidExample[] = [\n  {\n    id: 'flowchart-basic',\n    title: '基础流程图',\n    description: '简单的流程图示例',\n    category: '流程图',\n    code: `flowchart TD\n    A[开始] --> B{是否满足条件?}\n    B -->|是| C[执行操作]\n    B -->|否| D[跳过操作]\n    C --> E[结束]\n    D --> E`\n  },\n  {\n    id: 'sequence-basic',\n    title: '基础时序图',\n    description: '用户登录时序图',\n    category: '时序图',\n    code: `sequenceDiagram\n    participant U as 用户\n    participant F as 前端\n    participant B as 后端\n    participant D as 数据库\n    \n    U->>F: 输入用户名密码\n    F->>B: 发送登录请求\n    B->>D: 验证用户信息\n    D-->>B: 返回验证结果\n    B-->>F: 返回登录状态\n    F-->>U: 显示登录结果`\n  },\n  {\n    id: 'class-basic',\n    title: '基础类图',\n    description: '简单的类关系图',\n    category: '类图',\n    code: `classDiagram\n    class Animal {\n        +String name\n        +int age\n        +eat()\n        +sleep()\n    }\n    \n    class Dog {\n        +String breed\n        +bark()\n    }\n    \n    class Cat {\n        +String color\n        +meow()\n    }\n    \n    Animal <|-- Dog\n    Animal <|-- Cat`\n  },\n  {\n    id: 'gantt-basic',\n    title: '基础甘特图',\n    description: '项目进度甘特图',\n    category: '甘特图',\n    code: `gantt\n    title 项目开发进度\n    dateFormat YYYY-MM-DD\n    axisFormat %m/%d\n\n    section 设计\n    需求分析    :done, task1, 2024-01-01, 2024-01-05\n    UI设计     :done, task2, 2024-01-06, 2024-01-12\n\n    section 开发\n    前端开发    :active, task3, 2024-01-13, 2024-02-15\n    后端开发    :task4, 2024-01-20, 2024-02-20\n\n    section 测试\n    单元测试    :task5, 2024-02-16, 2024-02-25\n    集成测试    :task6, 2024-02-21, 2024-03-01`\n  },\n  {\n    id: 'gantt-simple',\n    title: '简单甘特图',\n    description: '字体清晰的简化甘特图',\n    category: '甘特图',\n    code: `gantt\n    title 网站开发\n    dateFormat YYYY-MM-DD\n\n    section 阶段一\n    设计    :done, a1, 2024-01-01, 2024-01-07\n    开发    :active, a2, 2024-01-08, 2024-01-21\n\n    section 阶段二\n    测试    :a3, 2024-01-22, 2024-01-28\n    上线    :a4, 2024-01-29, 2024-01-31`\n  },\n  {\n    id: 'pie-basic',\n    title: '基础饼图',\n    description: '数据分布饼图',\n    category: '饼图',\n    code: `pie title 编程语言使用分布\n    \"JavaScript\" : 35\n    \"Python\" : 25\n    \"Java\" : 20\n    \"TypeScript\" : 15\n    \"其他\" : 5`\n  },\n  {\n    id: 'state-basic',\n    title: '基础状态图',\n    description: '订单状态流转图',\n    category: '状态图',\n    code: `stateDiagram-v2\n    [*] --> 待支付\n    待支付 --> 已支付 : 支付成功\n    待支付 --> 已取消 : 取消订单\n    已支付 --> 已发货 : 商家发货\n    已支付 --> 已退款 : 申请退款\n    已发货 --> 已完成 : 确认收货\n    已发货 --> 已退款 : 退货退款\n    已完成 --> [*]\n    已取消 --> [*]\n    已退款 --> [*]`\n  },\n  {\n    id: 'er-basic',\n    title: '基础ER图',\n    description: '数据库实体关系图',\n    category: 'ER图',\n    code: `erDiagram\n    USER {\n        int id PK\n        string username\n        string email\n        datetime created_at\n    }\n    \n    POST {\n        int id PK\n        string title\n        text content\n        int user_id FK\n        datetime created_at\n    }\n    \n    COMMENT {\n        int id PK\n        text content\n        int post_id FK\n        int user_id FK\n        datetime created_at\n    }\n    \n    USER ||--o{ POST : creates\n    POST ||--o{ COMMENT : has\n    USER ||--o{ COMMENT : writes`\n  },\n  {\n    id: 'journey-basic',\n    title: '基础用户旅程图',\n    description: '用户购物旅程',\n    category: '旅程图',\n    code: `journey\n    title 用户购物旅程\n    section 发现阶段\n      浏览商品: 5: 用户\n      搜索产品: 3: 用户\n      查看详情: 4: 用户\n    section 决策阶段\n      比较价格: 2: 用户\n      查看评价: 4: 用户\n      咨询客服: 3: 用户, 客服\n    section 购买阶段\n      加入购物车: 5: 用户\n      结算支付: 3: 用户\n      确认订单: 5: 用户\n    section 售后阶段\n      物流跟踪: 4: 用户\n      确认收货: 5: 用户\n      评价商品: 4: 用户`\n  }\n];\n\nexport const getExamplesByCategory = () => {\n  const categories = [...new Set(mermaidExamples.map(example => example.category))];\n  return categories.reduce((acc, category) => {\n    acc[category] = mermaidExamples.filter(example => example.category === category);\n    return acc;\n  }, {} as Record<string, MermaidExample[]>);\n};\n\nexport const getExampleById = (id: string) => {\n  return mermaidExamples.find(example => example.id === id);\n};\n\nexport const defaultMermaidCode = `flowchart TD\n    A[开始] --> B{是否满足条件?}\n    B -->|是| C[执行操作]\n    B -->|否| D[跳过操作]\n    C --> E[结束]\n    D --> E`;\n"], "names": [], "mappings": ";;;;;;;;;;AAQO,MAAM,kBAAoC;IAC/C;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;WAKA,CAAC;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;;kBAWO,CAAC;IACjB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;;;;;;;;;;mBAmBQ,CAAC;IAClB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;;;;;;0CAe+B,CAAC;IACzC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;qCAU0B,CAAC;IACpC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;YAKC,CAAC;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;eAUI,CAAC;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;gCA0BqB,CAAC;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM,CAAC;;;;;;;;;;;;;;;;;iBAiBM,CAAC;IAChB;CACD;AAEM,MAAM,wBAAwB;IACnC,MAAM,aAAa;WAAI,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAA,UAAW,QAAQ,QAAQ;KAAG;IACjF,OAAO,WAAW,MAAM,CAAC,CAAC,KAAK;QAC7B,GAAG,CAAC,SAAS,GAAG,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QACvE,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACxD;AAEO,MAAM,qBAAqB,CAAC;;;;;WAKxB,CAAC", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport LZString from 'lz-string';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 复制到剪贴板\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text);\n      return true;\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n      const result = document.execCommand('copy');\n      textArea.remove();\n      return result;\n    }\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error);\n    return false;\n  }\n}\n\n// 下载文件\nexport function downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {\n  const blob = new Blob([content], { type: mimeType });\n  const url = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(url);\n}\n\n// 压缩和解压缩代码用于URL分享\nexport function compressCode(code: string): string {\n  try {\n    return LZString.compressToEncodedURIComponent(code);\n  } catch (error) {\n    console.error('Failed to compress code:', error);\n    return encodeURIComponent(code);\n  }\n}\n\nexport function decompressCode(compressed: string): string {\n  try {\n    const decompressed = LZString.decompressFromEncodedURIComponent(compressed);\n    return decompressed || decodeURIComponent(compressed);\n  } catch (error) {\n    console.error('Failed to decompress code:', error);\n    try {\n      return decodeURIComponent(compressed);\n    } catch {\n      return compressed;\n    }\n  }\n}\n\n// 生成分享链接\nexport function generateShareUrl(code: string): string {\n  const compressed = compressCode(code);\n  const baseUrl = typeof window !== 'undefined' ? window.location.origin + window.location.pathname : '';\n  return `${baseUrl}?code=${compressed}`;\n}\n\n// 从URL获取代码\nexport function getCodeFromUrl(): string | null {\n  if (typeof window === 'undefined') return null;\n  \n  const urlParams = new URLSearchParams(window.location.search);\n  const compressed = urlParams.get('code');\n  \n  if (compressed) {\n    return decompressCode(compressed);\n  }\n  \n  return null;\n}\n\n// 本地存储相关\nexport const STORAGE_KEYS = {\n  LAST_CODE: 'mermaid-editor-last-code',\n  THEME: 'mermaid-editor-theme',\n  EDITOR_SETTINGS: 'mermaid-editor-settings',\n} as const;\n\nexport function saveToLocalStorage(key: string, value: any): void {\n  try {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(key, JSON.stringify(value));\n    }\n  } catch (error) {\n    console.error('Failed to save to localStorage:', error);\n  }\n}\n\nexport function loadFromLocalStorage<T>(key: string, defaultValue: T): T {\n  try {\n    if (typeof window !== 'undefined') {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue;\n    }\n  } catch (error) {\n    console.error('Failed to load from localStorage:', error);\n  }\n  return defaultValue;\n}\n\n// 格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 获取当前时间戳字符串\nexport function getTimestamp(): string {\n  return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);\n}\n\n// 验证是否为有效的URL\nexport function isValidUrl(string: string): boolean {\n  try {\n    new URL(string);\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,iNAAO,EAAC,IAAA,wLAAI,EAAC;AACtB;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;YACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,OAAO;QACT,OAAO;YACL,OAAO;YACP,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;YACtB,SAAS,KAAK,CAAC,GAAG,GAAG;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,KAAK;YACd,SAAS,MAAM;YACf,MAAM,SAAS,SAAS,WAAW,CAAC;YACpC,SAAS,MAAM;YACf,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,WAAmB,YAAY;IAC7F,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,aAAa,IAAY;IACvC,IAAI;QACF,OAAO,0MAAQ,CAAC,6BAA6B,CAAC;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,mBAAmB;IAC5B;AACF;AAEO,SAAS,eAAe,UAAkB;IAC/C,IAAI;QACF,MAAM,eAAe,0MAAQ,CAAC,iCAAiC,CAAC;QAChE,OAAO,gBAAgB,mBAAmB;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,IAAI;YACF,OAAO,mBAAmB;QAC5B,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAGO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,aAAa,aAAa;IAChC,MAAM,UAAU,sCAAgC,0BAAoD;IACpG,OAAO,GAAG,QAAQ,MAAM,EAAE,YAAY;AACxC;AAGO,SAAS;IACd,wCAAmC,OAAO;;;IAE1C,MAAM;IACN,MAAM;AAOR;AAGO,MAAM,eAAe;IAC1B,WAAW;IACX,OAAO;IACP,iBAAiB;AACnB;AAEO,SAAS,mBAAmB,GAAW,EAAE,KAAU;IACxD,IAAI;QACF;;IAGF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;IACnD;AACF;AAEO,SAAS,qBAAwB,GAAW,EAAE,YAAe;IAClE,IAAI;QACF;;IAIF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IACA,OAAO;AACT;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS;IACd,OAAO,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG,CAAC;AAClE;AAGO,SAAS,WAAW,MAAc;IACvC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/Toolbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTheme } from 'next-themes';\nimport html2canvas from 'html2canvas';\nimport domtoimage from 'dom-to-image';\nimport { \n  copyToClipboard, \n  downloadFile, \n  generateShareUrl, \n  getTimestamp \n} from '../lib/utils';\n\ninterface ToolbarProps {\n  code: string;\n  onCodeChange: (code: string) => void;\n  previewRef: React.RefObject<HTMLDivElement>;\n  currentSvg?: string;\n}\n\nexport default function Toolbar({ code, onCodeChange, previewRef, currentSvg }: ToolbarProps) {\n  const { theme, setTheme } = useTheme();\n  const [isExporting, setIsExporting] = useState(false);\n  const [showShareModal, setShowShareModal] = useState(false);\n  const [shareUrl, setShareUrl] = useState('');\n\n  // 导出为 PNG\n  const exportToPNG = async () => {\n    if (!previewRef.current) return;\n\n    setIsExporting(true);\n    try {\n      const svgElement = previewRef.current.querySelector('svg');\n      if (!svgElement) {\n        throw new Error('未找到可导出的图表');\n      }\n\n      // 使用 html2canvas 导出\n      const canvas = await html2canvas(svgElement as HTMLElement, {\n        backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',\n        scale: 2, // 提高分辨率\n        useCORS: true,\n        allowTaint: true,\n      });\n\n      // 转换为 blob 并下载\n      canvas.toBlob((blob) => {\n        if (blob) {\n          const url = URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `mermaid-diagram-${getTimestamp()}.png`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          URL.revokeObjectURL(url);\n        }\n      }, 'image/png');\n    } catch (error) {\n      console.error('PNG 导出失败:', error);\n      alert('PNG 导出失败，请重试');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  // 导出为 SVG\n  const exportToSVG = () => {\n    if (!currentSvg) {\n      alert('没有可导出的图表');\n      return;\n    }\n\n    try {\n      // 创建完整的 SVG 文件内容\n      const svgContent = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n${currentSvg}`;\n\n      downloadFile(svgContent, `mermaid-diagram-${getTimestamp()}.svg`, 'image/svg+xml');\n    } catch (error) {\n      console.error('SVG 导出失败:', error);\n      alert('SVG 导出失败，请重试');\n    }\n  };\n\n  // 导出代码\n  const exportCode = () => {\n    if (!code.trim()) {\n      alert('没有可导出的代码');\n      return;\n    }\n\n    downloadFile(code, `mermaid-code-${getTimestamp()}.mmd`, 'text/plain');\n  };\n\n  // 复制代码到剪贴板\n  const copyCode = async () => {\n    if (!code.trim()) {\n      alert('没有可复制的代码');\n      return;\n    }\n\n    const success = await copyToClipboard(code);\n    if (success) {\n      // 这里可以添加一个 toast 通知\n      alert('代码已复制到剪贴板');\n    } else {\n      alert('复制失败，请重试');\n    }\n  };\n\n  // 生成分享链接\n  const generateShare = () => {\n    if (!code.trim()) {\n      alert('没有可分享的内容');\n      return;\n    }\n\n    try {\n      const url = generateShareUrl(code);\n      setShareUrl(url);\n      setShowShareModal(true);\n    } catch (error) {\n      console.error('生成分享链接失败:', error);\n      alert('生成分享链接失败，请重试');\n    }\n  };\n\n  // 复制分享链接\n  const copyShareUrl = async () => {\n    const success = await copyToClipboard(shareUrl);\n    if (success) {\n      alert('分享链接已复制到剪贴板');\n      setShowShareModal(false);\n    } else {\n      alert('复制失败，请重试');\n    }\n  };\n\n  // 清空编辑器\n  const clearEditor = () => {\n    if (code.trim() && confirm('确定要清空编辑器吗？')) {\n      onCodeChange('');\n    }\n  };\n\n  return (\n    <>\n      <div className=\"flex items-center justify-between p-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700\">\n        {/* 左侧：标题 */}\n        <div className=\"flex items-center space-x-4\">\n          <h1 className=\"text-xl font-bold text-gray-900 dark:text-gray-100\">\n            Mermaid 在线编辑器\n          </h1>\n          <div className=\"hidden sm:block text-sm text-gray-500 dark:text-gray-400\">\n            实时编辑和预览 Mermaid 图表\n          </div>\n        </div>\n\n        {/* 右侧：工具按钮 */}\n        <div className=\"flex items-center space-x-2\">\n          {/* 导出菜单 */}\n          <div className=\"relative group\">\n            <button\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n              disabled={isExporting}\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <span>{isExporting ? '导出中...' : '导出'}</span>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n            \n            {/* 下拉菜单 */}\n            <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10\">\n              <div className=\"py-1\">\n                <button\n                  onClick={exportToPNG}\n                  disabled={isExporting}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50\"\n                >\n                  导出为 PNG\n                </button>\n                <button\n                  onClick={exportToSVG}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  导出为 SVG\n                </button>\n                <button\n                  onClick={exportCode}\n                  className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  导出代码\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* 复制代码 */}\n          <button\n            onClick={copyCode}\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            title=\"复制代码\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n            </svg>\n            <span className=\"hidden sm:inline\">复制</span>\n          </button>\n\n          {/* 分享 */}\n          <button\n            onClick={generateShare}\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            title=\"生成分享链接\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n            </svg>\n            <span className=\"hidden sm:inline\">分享</span>\n          </button>\n\n          {/* 清空 */}\n          <button\n            onClick={clearEditor}\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors\"\n            title=\"清空编辑器\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n            <span className=\"hidden sm:inline\">清空</span>\n          </button>\n\n          {/* 主题切换 */}\n          <button\n            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n            className=\"p-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            title=\"切换主题\"\n          >\n            {theme === 'dark' ? (\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n              </svg>\n            ) : (\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n              </svg>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* 分享模态框 */}\n      {showShareModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">\n              分享链接\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              复制下面的链接来分享您的 Mermaid 图表：\n            </p>\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={shareUrl}\n                readOnly\n                className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm\"\n              />\n              <button\n                onClick={copyShareUrl}\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm\"\n              >\n                复制\n              </button>\n            </div>\n            <div className=\"flex justify-end mt-4\">\n              <button\n                onClick={() => setShowShareModal(false)}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n              >\n                关闭\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAoBe,SAAS,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAgB;IAC1F,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,uMAAQ;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,4PAAQ,EAAC;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,4PAAQ,EAAC;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,4PAAQ,EAAC;IAEzC,UAAU;IACV,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,eAAe;QACf,IAAI;YACF,MAAM,aAAa,WAAW,OAAO,CAAC,aAAa,CAAC;YACpD,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM;YAClB;YAEA,oBAAoB;YACpB,MAAM,SAAS,MAAM,IAAA,+MAAW,EAAC,YAA2B;gBAC1D,iBAAiB,UAAU,SAAS,YAAY;gBAChD,OAAO;gBACP,SAAS;gBACT,YAAY;YACd;YAEA,eAAe;YACf,OAAO,MAAM,CAAC,CAAC;gBACb,IAAI,MAAM;oBACR,MAAM,MAAM,IAAI,eAAe,CAAC;oBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,IAAI,GAAG;oBACZ,KAAK,QAAQ,GAAG,CAAC,gBAAgB,EAAE,IAAA,uKAAY,IAAG,IAAI,CAAC;oBACvD,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,KAAK,KAAK;oBACV,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,IAAI,eAAe,CAAC;gBACtB;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,UAAU;IACV,MAAM,cAAc;QAClB,IAAI,CAAC,YAAY;YACf,MAAM;YACN;QACF;QAEA,IAAI;YACF,iBAAiB;YACjB,MAAM,aAAa,CAAC;;AAE1B,EAAE,YAAY;YAER,IAAA,uKAAY,EAAC,YAAY,CAAC,gBAAgB,EAAE,IAAA,uKAAY,IAAG,IAAI,CAAC,EAAE;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,MAAM;YACN;QACF;QAEA,IAAA,uKAAY,EAAC,MAAM,CAAC,aAAa,EAAE,IAAA,uKAAY,IAAG,IAAI,CAAC,EAAE;IAC3D;IAEA,WAAW;IACX,MAAM,WAAW;QACf,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,MAAM;YACN;QACF;QAEA,MAAM,UAAU,MAAM,IAAA,0KAAe,EAAC;QACtC,IAAI,SAAS;YACX,oBAAoB;YACpB,MAAM;QACR,OAAO;YACL,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,MAAM,IAAA,2KAAgB,EAAC;YAC7B,YAAY;YACZ,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,MAAM,UAAU,MAAM,IAAA,0KAAe,EAAC;QACtC,IAAI,SAAS;YACX,MAAM;YACN,kBAAkB;QACpB,OAAO;YACL,MAAM;QACR;IACF;IAEA,QAAQ;IACR,MAAM,cAAc;QAClB,IAAI,KAAK,IAAI,MAAM,QAAQ,eAAe;YACxC,aAAa;QACf;IACF;IAEA,qBACE;;0BACE,yRAAC;gBAAI,WAAU;;kCAEb,yRAAC;wBAAI,WAAU;;0CACb,yRAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,yRAAC;gCAAI,WAAU;0CAA2D;;;;;;;;;;;;kCAM5E,yRAAC;wBAAI,WAAU;;0CAEb,yRAAC;gCAAI,WAAU;;kDACb,yRAAC;wCACC,WAAU;wCACV,UAAU;;0DAEV,yRAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,yRAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,yRAAC;0DAAM,cAAc,WAAW;;;;;;0DAChC,yRAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,yRAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAKzE,yRAAC;wCAAI,WAAU;kDACb,cAAA,yRAAC;4CAAI,WAAU;;8DACb,yRAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DACX;;;;;;8DAGD,yRAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,yRAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAQP,yRAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;;kDAEN,yRAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,yRAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,yRAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAIrC,yRAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;;kDAEN,yRAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,yRAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,yRAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAIrC,yRAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;;kDAEN,yRAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,yRAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,yRAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAIrC,yRAAC;gCACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;gCACrD,WAAU;gCACV,OAAM;0CAEL,UAAU,uBACT,yRAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,yRAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;yDAGvE,yRAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,yRAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,gCACC,yRAAC;gBAAI,WAAU;0BACb,cAAA,yRAAC;oBAAI,WAAU;;sCACb,yRAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,yRAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAG7D,yRAAC;4BAAI,WAAU;;8CACb,yRAAC;oCACC,MAAK;oCACL,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,yRAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAIH,yRAAC;4BAAI,WAAU;sCACb,cAAA,yRAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/Examples.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { mermaidExamples, getExamplesByCategory, type MermaidExample } from '../lib/examples';\n\ninterface ExamplesProps {\n  onSelectExample: (code: string) => void;\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\nexport default function Examples({ onSelectExample, isOpen, onToggle }: ExamplesProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('流程图');\n  const examplesByCategory = getExamplesByCategory();\n  const categories = Object.keys(examplesByCategory);\n\n  const handleExampleClick = (example: MermaidExample) => {\n    onSelectExample(example.code);\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-white dark:bg-gray-900\">\n      {/* 分类标签 */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex flex-wrap gap-2\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              onClick={() => setSelectedCategory(category)}\n              className={`\n                px-3 py-1 text-sm rounded-full transition-colors\n                ${selectedCategory === category\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'\n                }\n              `}\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 示例列表 - 水平滚动 */}\n      <div className=\"flex-1 overflow-hidden\">\n        <div className=\"h-full overflow-x-auto overflow-y-hidden\">\n          <div className=\"flex gap-4 p-4 h-full\">\n            {examplesByCategory[selectedCategory]?.map((example) => (\n              <div\n                key={example.id}\n                onClick={() => handleExampleClick(example)}\n                className=\"flex-shrink-0 w-80 p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group h-fit\"\n              >\n                <div className=\"flex items-start justify-between mb-2\">\n                  <h3 className=\"font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400\">\n                    {example.title}\n                  </h3>\n                  <svg className=\"w-4 h-4 text-gray-400 group-hover:text-blue-500 flex-shrink-0 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                  {example.description}\n                </p>\n                <div className=\"bg-gray-50 dark:bg-gray-800 rounded p-3\">\n                  <pre className=\"text-xs text-gray-700 dark:text-gray-300 overflow-hidden\">\n                    <code className=\"line-clamp-3\">\n                      {example.code.split('\\n').slice(0, 3).join('\\n')}\n                      {example.code.split('\\n').length > 3 && '\\n...'}\n                    </code>\n                  </pre>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 底部提示 */}\n      <div className=\"p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800\">\n        <div className=\"text-xs text-gray-500 dark:text-gray-400 text-center\">\n          点击示例可快速加载到编辑器 • 共 {examplesByCategory[selectedCategory]?.length || 0} 个示例\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// 添加 CSS 类用于文本截断\nconst styles = `\n  .line-clamp-4 {\n    display: -webkit-box;\n    -webkit-line-clamp: 4;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n`;\n\n// 在组件挂载时添加样式\nif (typeof document !== 'undefined') {\n  const styleSheet = document.createElement('style');\n  styleSheet.textContent = styles;\n  document.head.appendChild(styleSheet);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,SAAS,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,4PAAQ,EAAS;IACjE,MAAM,qBAAqB,IAAA,mLAAqB;IAChD,MAAM,aAAa,OAAO,IAAI,CAAC;IAE/B,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB,QAAQ,IAAI;IAC9B;IAEA,qBACE,yRAAC;QAAI,WAAU;;0BAEb,yRAAC;gBAAI,WAAU;0BACb,cAAA,yRAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,yRAAC;4BAEC,SAAS,IAAM,oBAAoB;4BACnC,WAAW,CAAC;;gBAEV,EAAE,qBAAqB,WACnB,2BACA,yGACH;cACH,CAAC;sCAEA;2BAVI;;;;;;;;;;;;;;;0BAiBb,yRAAC;gBAAI,WAAU;0BACb,cAAA,yRAAC;oBAAI,WAAU;8BACb,cAAA,yRAAC;wBAAI,WAAU;kCACZ,kBAAkB,CAAC,iBAAiB,EAAE,IAAI,CAAC,wBAC1C,yRAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,yRAAC;wCAAI,WAAU;;0DACb,yRAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,yRAAC;gDAAI,WAAU;gDAAqE,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5H,cAAA,yRAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAGzE,yRAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAEtB,yRAAC;wCAAI,WAAU;kDACb,cAAA,yRAAC;4CAAI,WAAU;sDACb,cAAA,yRAAC;gDAAK,WAAU;;oDACb,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;oDAC1C,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,KAAK;;;;;;;;;;;;;;;;;;+BAnBzC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;0BA8BzB,yRAAC;gBAAI,WAAU;0BACb,cAAA,yRAAC;oBAAI,WAAU;;wBAAuD;wBACjD,kBAAkB,CAAC,iBAAiB,EAAE,UAAU;wBAAE;;;;;;;;;;;;;;;;;;AAK/E;AAEA,iBAAiB;AACjB,MAAM,SAAS,CAAC;;;;;;;AAOhB,CAAC;AAED,aAAa;AACb,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,aAAa,SAAS,aAAa,CAAC;IAC1C,WAAW,WAAW,GAAG;IACzB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport dynamic from 'next/dynamic';\nimport { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';\nimport { defaultMermaidCode } from '../lib/examples';\nimport { getCodeFromUrl, saveToLocalStorage, loadFromLocalStorage, STORAGE_KEYS } from '../lib/utils';\nimport Toolbar from '../components/Toolbar';\nimport Examples from '../components/Examples';\n\n// 动态导入组件以避免 SSR 问题\nconst Editor = dynamic(() => import('../components/Editor'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"h-full flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n    </div>\n  )\n});\n\nconst Preview = dynamic(() => import('../components/Preview'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"h-full flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n    </div>\n  )\n});\n\nexport default function Home() {\n  const [code, setCode] = useState<string>('');\n  const [currentSvg, setCurrentSvg] = useState<string>('');\n  const [isExamplesOpen, setIsExamplesOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor');\n  const previewRef = useRef<HTMLDivElement>(null);\n\n  // 初始化代码\n  useEffect(() => {\n    const initializeCode = () => {\n      // 首先尝试从 URL 获取代码\n      const urlCode = getCodeFromUrl();\n      if (urlCode) {\n        setCode(urlCode);\n        return;\n      }\n\n      // 然后尝试从本地存储获取\n      const savedCode = loadFromLocalStorage(STORAGE_KEYS.LAST_CODE, '');\n      if (savedCode) {\n        setCode(savedCode);\n        return;\n      }\n\n      // 最后使用默认代码\n      setCode(defaultMermaidCode);\n    };\n\n    initializeCode();\n    setIsLoading(false);\n  }, []);\n\n  // 保存代码到本地存储\n  useEffect(() => {\n    if (code && !isLoading) {\n      saveToLocalStorage(STORAGE_KEYS.LAST_CODE, code);\n    }\n  }, [code, isLoading]);\n\n  const handleCodeChange = (newCode: string) => {\n    setCode(newCode);\n  };\n\n  const handleRenderComplete = (svg: string) => {\n    setCurrentSvg(svg);\n  };\n\n  const handleSelectExample = (exampleCode: string) => {\n    setCode(exampleCode);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">加载中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-screen flex flex-col bg-background\">\n      {/* 工具栏 */}\n      <Toolbar\n        code={code}\n        onCodeChange={handleCodeChange}\n        previewRef={previewRef}\n        currentSvg={currentSvg}\n      />\n\n      {/* 主要内容区域 - 上下分割 */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* 编辑器和预览区域 - 上半部分 */}\n        <div className=\"flex-1 min-h-0\">\n          {/* 桌面端：可调整大小的面板 */}\n          <div className=\"hidden md:block h-full\">\n            <PanelGroup direction=\"horizontal\" className=\"h-full\">\n              {/* 编辑器面板 */}\n              <Panel defaultSize={50} minSize={20} maxSize={80}>\n                <div className=\"h-full p-4\">\n                  <div className=\"h-full flex flex-col\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h2 className=\"text-lg font-semibold text-foreground\">\n                        代码编辑器\n                      </h2>\n                    </div>\n                    <div className=\"flex-1 min-h-0\">\n                      <Editor\n                        value={code}\n                        onChange={handleCodeChange}\n                      />\n                    </div>\n                  </div>\n                </div>\n              </Panel>\n\n              {/* 分割线 */}\n              <PanelResizeHandle className=\"w-2 bg-gray-200 dark:bg-gray-700 hover:bg-blue-400 dark:hover:bg-blue-600 transition-colors cursor-col-resize relative group\">\n                <div className=\"absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-gray-400 dark:bg-gray-500 group-hover:bg-blue-500 transition-colors\"></div>\n              </PanelResizeHandle>\n\n              {/* 预览面板 */}\n              <Panel defaultSize={50} minSize={20} maxSize={80}>\n                <div className=\"h-full p-4\">\n                  <div className=\"h-full flex flex-col\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h2 className=\"text-lg font-semibold text-foreground\">\n                        图表预览\n                      </h2>\n                      <div className=\"text-sm text-muted-foreground\">\n                        实时渲染\n                      </div>\n                    </div>\n                    <div className=\"flex-1 min-h-0\">\n                      <div ref={previewRef} className=\"h-full\">\n                        <Preview\n                          code={code}\n                          onRenderComplete={handleRenderComplete}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </Panel>\n            </PanelGroup>\n          </div>\n\n          {/* 移动端：标签页切换 */}\n          <div className=\"md:hidden h-full flex flex-col\">\n            {/* 标签页头部 */}\n            <div className=\"flex border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900\">\n              <button\n                onClick={() => setActiveTab('editor')}\n                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${\n                  activeTab === 'editor'\n                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                }`}\n              >\n                代码编辑器\n              </button>\n              <button\n                onClick={() => setActiveTab('preview')}\n                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${\n                  activeTab === 'preview'\n                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                }`}\n              >\n                图表预览\n              </button>\n            </div>\n\n            {/* 标签页内容 */}\n            <div className=\"flex-1 min-h-0\">\n              {activeTab === 'editor' ? (\n                <div className=\"h-full p-4\">\n                  <div className=\"h-full flex flex-col\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n                        代码编辑器\n                      </h2>\n                    </div>\n                    <div className=\"flex-1 min-h-0\">\n                      <Editor\n                        value={code}\n                        onChange={handleCodeChange}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"h-full p-4\">\n                  <div className=\"h-full flex flex-col\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n                        图表预览\n                      </h2>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        实时渲染\n                      </div>\n                    </div>\n                    <div className=\"flex-1 min-h-0\">\n                      <div ref={previewRef} className=\"h-full\">\n                        <Preview\n                          code={code}\n                          onRenderComplete={handleRenderComplete}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* 示例模板区域 - 下半部分 */}\n        <div className={`border-t border-gray-200 dark:border-gray-700 transition-all duration-300 ${\n          isExamplesOpen ? 'h-80' : 'h-12'\n        }`}>\n          <div className=\"h-full flex flex-col\">\n            {/* 示例模板头部 */}\n            <div className=\"flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n                示例模板\n              </h3>\n              <button\n                onClick={() => setIsExamplesOpen(!isExamplesOpen)}\n                className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg transition-colors\"\n                title={isExamplesOpen ? \"收起示例\" : \"展开示例\"}\n              >\n                <svg\n                  className={`w-5 h-5 transition-transform duration-200 ${isExamplesOpen ? 'rotate-180' : ''}`}\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n                </svg>\n              </button>\n            </div>\n\n            {/* 示例模板内容 */}\n            {isExamplesOpen && (\n              <div className=\"flex-1 overflow-hidden\">\n                <Examples\n                  onSelectExample={handleSelectExample}\n                  isOpen={true}\n                  onToggle={() => setIsExamplesOpen(false)}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAUA,mBAAmB;AACnB,MAAM,SAAS,IAAA,qNAAO;;;;;;IACpB,KAAK;IACL,SAAS,kBACP,yRAAC;YAAI,WAAU;sBACb,cAAA,yRAAC;gBAAI,WAAU;;;;;;;;;;;;AAKrB,MAAM,UAAU,IAAA,qNAAO;;;;;;IACrB,KAAK;IACL,SAAS,kBACP,yRAAC;YAAI,WAAU;sBACb,cAAA,yRAAC;gBAAI,WAAU;;;;;;;;;;;;AAKN,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,4PAAQ,EAAS;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,4PAAQ,EAAS;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,4PAAQ,EAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,4PAAQ,EAAC;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,4PAAQ,EAAuB;IACjE,MAAM,aAAa,IAAA,0PAAM,EAAiB;IAE1C,QAAQ;IACR,IAAA,6PAAS,EAAC;QACR,MAAM,iBAAiB;YACrB,iBAAiB;YACjB,MAAM,UAAU,IAAA,yKAAc;YAC9B,IAAI,SAAS;gBACX,QAAQ;gBACR;YACF;YAEA,cAAc;YACd,MAAM,YAAY,IAAA,+KAAoB,EAAC,uKAAY,CAAC,SAAS,EAAE;YAC/D,IAAI,WAAW;gBACb,QAAQ;gBACR;YACF;YAEA,WAAW;YACX,QAAQ,gLAAkB;QAC5B;QAEA;QACA,aAAa;IACf,GAAG,EAAE;IAEL,YAAY;IACZ,IAAA,6PAAS,EAAC;QACR,IAAI,QAAQ,CAAC,WAAW;YACtB,IAAA,6KAAkB,EAAC,uKAAY,CAAC,SAAS,EAAE;QAC7C;IACF,GAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,uBAAuB,CAAC;QAC5B,cAAc;IAChB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ;IACV;IAEA,IAAI,WAAW;QACb,qBACE,yRAAC;YAAI,WAAU;sBACb,cAAA,yRAAC;gBAAI,WAAU;;kCACb,yRAAC;wBAAI,WAAU;;;;;;kCACf,yRAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,yRAAC;QAAI,WAAU;;0BAEb,yRAAC,4KAAO;gBACN,MAAM;gBACN,cAAc;gBACd,YAAY;gBACZ,YAAY;;;;;;0BAId,yRAAC;gBAAI,WAAU;;kCAEb,yRAAC;wBAAI,WAAU;;0CAEb,yRAAC;gCAAI,WAAU;0CACb,cAAA,yRAAC,6QAAU;oCAAC,WAAU;oCAAa,WAAU;;sDAE3C,yRAAC,wQAAK;4CAAC,aAAa;4CAAI,SAAS;4CAAI,SAAS;sDAC5C,cAAA,yRAAC;gDAAI,WAAU;0DACb,cAAA,yRAAC;oDAAI,WAAU;;sEACb,yRAAC;4DAAI,WAAU;sEACb,cAAA,yRAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;sEAIxD,yRAAC;4DAAI,WAAU;sEACb,cAAA,yRAAC;gEACC,OAAO;gEACP,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQpB,yRAAC,oRAAiB;4CAAC,WAAU;sDAC3B,cAAA,yRAAC;gDAAI,WAAU;;;;;;;;;;;sDAIjB,yRAAC,wQAAK;4CAAC,aAAa;4CAAI,SAAS;4CAAI,SAAS;sDAC5C,cAAA,yRAAC;gDAAI,WAAU;0DACb,cAAA,yRAAC;oDAAI,WAAU;;sEACb,yRAAC;4DAAI,WAAU;;8EACb,yRAAC;oEAAG,WAAU;8EAAwC;;;;;;8EAGtD,yRAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;sEAIjD,yRAAC;4DAAI,WAAU;sEACb,cAAA,yRAAC;gEAAI,KAAK;gEAAY,WAAU;0EAC9B,cAAA,yRAAC;oEACC,MAAM;oEACN,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWlC,yRAAC;gCAAI,WAAU;;kDAEb,yRAAC;wCAAI,WAAU;;0DACb,yRAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,uDAAuD,EACjE,cAAc,WACV,qFACA,iFACJ;0DACH;;;;;;0DAGD,yRAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,uDAAuD,EACjE,cAAc,YACV,qFACA,iFACJ;0DACH;;;;;;;;;;;;kDAMH,yRAAC;wCAAI,WAAU;kDACZ,cAAc,yBACb,yRAAC;4CAAI,WAAU;sDACb,cAAA,yRAAC;gDAAI,WAAU;;kEACb,yRAAC;wDAAI,WAAU;kEACb,cAAA,yRAAC;4DAAG,WAAU;sEAAyD;;;;;;;;;;;kEAIzE,yRAAC;wDAAI,WAAU;kEACb,cAAA,yRAAC;4DACC,OAAO;4DACP,UAAU;;;;;;;;;;;;;;;;;;;;;iEAMlB,yRAAC;4CAAI,WAAU;sDACb,cAAA,yRAAC;gDAAI,WAAU;;kEACb,yRAAC;wDAAI,WAAU;;0EACb,yRAAC;gEAAG,WAAU;0EAAyD;;;;;;0EAGvE,yRAAC;gEAAI,WAAU;0EAA2C;;;;;;;;;;;;kEAI5D,yRAAC;wDAAI,WAAU;kEACb,cAAA,yRAAC;4DAAI,KAAK;4DAAY,WAAU;sEAC9B,cAAA,yRAAC;gEACC,MAAM;gEACN,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYpC,yRAAC;wBAAI,WAAW,CAAC,0EAA0E,EACzF,iBAAiB,SAAS,QAC1B;kCACA,cAAA,yRAAC;4BAAI,WAAU;;8CAEb,yRAAC;oCAAI,WAAU;;sDACb,yRAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAGvE,yRAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;4CACV,OAAO,iBAAiB,SAAS;sDAEjC,cAAA,yRAAC;gDACC,WAAW,CAAC,0CAA0C,EAAE,iBAAiB,eAAe,IAAI;gDAC5F,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,yRAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gCAM1E,gCACC,yRAAC;oCAAI,WAAU;8CACb,cAAA,yRAAC,6KAAQ;wCACP,iBAAiB;wCACjB,QAAQ;wCACR,UAAU,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpD", "debugId": null}}]}