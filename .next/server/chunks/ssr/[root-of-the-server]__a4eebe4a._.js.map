{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/libre_baskerville_5c909cfe.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"libre_baskerville_5c909cfe-module__EPaFoa__className\",\n  \"variable\": \"libre_baskerville_5c909cfe-module__EPaFoa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/libre_baskerville_5c909cfe.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Libre_Baskerville%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22700%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22libreBaskerville%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Libre Baskerville', 'Libre Baskerville Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,4KAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,4KAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,4KAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/lora_9f0ff0ff.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"lora_9f0ff0ff-module__0CWRvq__className\",\n  \"variable\": \"lora_9f0ff0ff-module__0CWRvq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/lora_9f0ff0ff.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Lora%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-serif%22}],%22variableName%22:%22lora%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Lora', 'Lora Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,+JAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ibm_plex_mono_ab1df9df.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"ibm_plex_mono_ab1df9df-module__imkQAa__className\",\n  \"variable\": \"ibm_plex_mono_ab1df9df-module__imkQAa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ibm_plex_mono_ab1df9df.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22IBM_Plex_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22],%22variable%22:%22--font-mono%22}],%22variableName%22:%22ibmPlexMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'IBM Plex Mono', 'IBM Plex Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,mTAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/out_web/mermaid_online_cc/components/ThemeProvider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,mTAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Libre_Baskerville, Lora, IBM_Plex_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"../components/ThemeProvider\";\n\nconst libreBaskerville = Libre_Baskerville({\n  subsets: [\"latin\"],\n  weight: [\"400\", \"700\"],\n  variable: \"--font-sans\",\n});\n\nconst lora = Lora({\n  subsets: [\"latin\"],\n  variable: \"--font-serif\",\n});\n\nconst ibmPlexMono = IBM_Plex_Mono({\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\"],\n  variable: \"--font-mono\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Mermaid 在线编辑器\",\n  description: \"实时编辑和预览 Mermaid 图表的在线工具\",\n  keywords: [\"mermaid\", \"diagram\", \"flowchart\", \"editor\", \"online\"],\n  authors: [{ name: \"Mermaid Online Editor\" }],\n  viewport: \"width=device-width, initial-scale=1\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\" suppressHydrationWarning>\n      <body\n        className={`${libreBaskerville.variable} ${lora.variable} ${ibmPlexMono.variable} font-sans antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"light\"\n          enableSystem={false}\n          disableTransitionOnChange={false}\n        >\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAGA;;;;;;;AAmBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAW;QAAW;QAAa;QAAU;KAAS;IACjE,SAAS;QAAC;YAAE,MAAM;QAAwB;KAAE;IAC5C,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,yRAAC;QAAK,MAAK;QAAQ,wBAAwB;kBACzC,cAAA,yRAAC;YACC,WAAW,GAAG,gKAAgB,CAAC,QAAQ,CAAC,CAAC,EAAE,mJAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,4JAAW,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBAExG,cAAA,yRAAC,wLAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,cAAc;gBACd,2BAA2B;0BAE1B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,8JACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}