var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/6a801_779c275a._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Desktop_out_web_mermaid_online_cc_app_42fe1a8a._.js")
R.c("server/chunks/ssr/[root-of-the-server]__a4eebe4a._.js")
R.c("server/chunks/ssr/6a801_next_dist_client_components_2212ce17._.js")
R.c("server/chunks/ssr/6a801_next_dist_client_components_builtin_forbidden_180b4c11.js")
R.c("server/chunks/ssr/6a801_next_dist_client_components_builtin_unauthorized_de541931.js")
R.c("server/chunks/ssr/6a801_next_dist_client_components_builtin_global-error_2d13258e.js")
R.c("server/chunks/ssr/6a801_next_dist_e37f5820._.js")
R.c("server/chunks/ssr/[root-of-the-server]__c2392212._.js")
R.m("[project]/Desktop/out_web/mermaid_online_cc/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/out_web/mermaid_online_cc/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/out_web/mermaid_online_cc/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Desktop/out_web/mermaid_online_cc/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/out_web/mermaid_online_cc/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/out_web/mermaid_online_cc/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/out_web/mermaid_online_cc/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Desktop/out_web/mermaid_online_cc/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/out_web/mermaid_online_cc/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
