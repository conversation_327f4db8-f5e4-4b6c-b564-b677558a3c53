(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/Desktop/out_web/mermaid_online_cc/lib/mermaid-config.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "baseMermaidConfig",
    ()=>baseMermaidConfig,
    "darkThemeConfig",
    ()=>darkThemeConfig,
    "initializeMermaid",
    ()=>initializeMermaid,
    "lightThemeConfig",
    ()=>lightThemeConfig,
    "renderMermaid",
    ()=>renderMermaid,
    "validateMermaidSyntax",
    ()=>validateMermaidSyntax
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/mermaid.core.mjs [app-client] (ecmascript)");
;
const lightThemeConfig = {
    theme: 'default',
    themeVariables: {
        // 基础颜色 - 使用新的设计系统
        primaryColor: '#8B7355',
        primaryTextColor: '#3C3A35',
        primaryBorderColor: '#C4BFB6',
        lineColor: '#8B8680',
        sectionBkgColor: '#F5F4F2',
        altSectionBkgColor: '#FEFEFE',
        gridColor: '#C4BFB6',
        secondaryColor: '#E8E5E0',
        tertiaryColor: '#FEFEFE',
        // 甘特图专用配置
        cScale0: '#8B7355',
        cScale1: '#7A9B76',
        cScale2: '#B8860B',
        cScale3: '#CD5C5C',
        cScale4: '#9370DB',
        cScale5: '#4682B4',
        taskBkgColor: '#8B7355',
        taskTextColor: '#FFFFFF',
        taskTextLightColor: '#3C3A35',
        taskTextOutsideColor: '#3C3A35',
        taskTextClickableColor: '#3C3A35',
        activeTaskBkgColor: '#7A9B76',
        activeTaskBorderColor: '#6B8E6B',
        gridColor: '#C4BFB6',
        section0: '#F5F4F2',
        section1: '#E8E5E0',
        section2: '#C4BFB6',
        section3: '#A8A29E',
        todayLineColor: '#CD5C5C',
        // 旅程图配置
        journeyLabelColor: '#3C3A35',
        journeyLabelBackground: '#FEFEFE',
        journeyLabelBorder: '#C4BFB6',
        journeyTaskBackground: '#8B7355',
        journeyTaskBorder: '#7A6B4F',
        journeyTaskTextColor: '#FFFFFF',
        journeySectionBackground: '#F5F4F2',
        journeySectionBorder: '#C4BFB6'
    }
};
const darkThemeConfig = {
    theme: 'dark',
    themeVariables: {
        // 基础颜色 - 使用新的设计系统暗色主题
        primaryColor: '#B5A082',
        primaryTextColor: '#F5F4F2',
        primaryBorderColor: '#5A5651',
        lineColor: '#C4BFB6',
        sectionBkgColor: '#4A453F',
        altSectionBkgColor: '#3C3731',
        gridColor: '#5A5651',
        secondaryColor: '#5A5651',
        tertiaryColor: '#4A453F',
        // 甘特图专用配置
        cScale0: '#B5A082',
        cScale1: '#9BB894',
        cScale2: '#D4AF37',
        cScale3: '#F08080',
        cScale4: '#DDA0DD',
        cScale5: '#87CEEB',
        taskBkgColor: '#B5A082',
        taskTextColor: '#FFFFFF',
        taskTextLightColor: '#F5F4F2',
        taskTextOutsideColor: '#F5F4F2',
        taskTextClickableColor: '#F5F4F2',
        activeTaskBkgColor: '#9BB894',
        activeTaskBorderColor: '#8AA883',
        gridColor: '#6B7280',
        section0: '#5A5651',
        section1: '#6B7280',
        section2: '#9CA3AF',
        section3: '#C4BFB6',
        todayLineColor: '#F08080',
        // 旅程图配置
        journeyLabelColor: '#F5F4F2',
        journeyLabelBackground: '#5A5651',
        journeyLabelBorder: '#6B7280',
        journeyTaskBackground: '#B5A082',
        journeyTaskBorder: '#A69175',
        journeyTaskTextColor: '#FFFFFF',
        journeySectionBackground: '#4A453F',
        journeySectionBorder: '#5A5651'
    }
};
const baseMermaidConfig = {
    startOnLoad: false,
    securityLevel: 'loose',
    fontFamily: 'Libre Baskerville, serif',
    fontSize: 14,
    htmlLabels: true,
    flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
    },
    sequence: {
        useMaxWidth: true,
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        useMaxWidth: true,
        leftPadding: 120,
        gridLineStartPadding: 35,
        fontSize: 14,
        sectionFontSize: 16,
        numberSectionStyles: 4,
        axisFormat: '%m/%d',
        tickInterval: '1day',
        weekday: 'monday',
        bottomPadding: 25,
        rightPadding: 75,
        topPadding: 75
    },
    class: {
        useMaxWidth: true
    },
    state: {
        useMaxWidth: true
    },
    pie: {
        useMaxWidth: true
    },
    er: {
        useMaxWidth: true
    },
    journey: {
        useMaxWidth: true,
        diagramMarginX: 50,
        diagramMarginY: 10,
        leftMargin: 150,
        width: 150,
        height: 50,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35,
        bottomMarginAdj: 1,
        rightAngles: false,
        taskFontSize: 14,
        taskFontFamily: 'Inter, sans-serif',
        taskMargin: 50,
        activationWidth: 10,
        textPlacement: 'fo',
        actorColours: [
            '#8FBC8F',
            '#FFB347',
            '#87CEEB',
            '#DDA0DD',
            '#F0E68C'
        ],
        sectionColours: [
            '#fff2cc',
            '#f8cecc',
            '#e1d5e7',
            '#dae8fc',
            '#d5e8d4'
        ]
    }
};
const initializeMermaid = function() {
    let isDark = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
    const themeConfig = isDark ? darkThemeConfig : lightThemeConfig;
    __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].initialize({
        ...baseMermaidConfig,
        ...themeConfig
    });
};
const renderMermaid = async function(element, code) {
    let id = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'mermaid-diagram';
    try {
        // 清除之前的内容
        element.innerHTML = '';
        // 渲染新的图表
        const { svg, bindFunctions } = await __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$mermaid$2f$dist$2f$mermaid$2e$core$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].render(id, code);
        element.innerHTML = svg;
        // 如果有绑定函数，执行它们
        if (bindFunctions) {
            bindFunctions(element);
        }
        return {
            svg,
            bindFunctions
        };
    } catch (error) {
        console.error('Mermaid rendering error:', error);
        element.innerHTML = '\n      <div class="flex items-center justify-center h-full text-red-500 bg-red-50 dark:bg-red-900/20 rounded-lg p-4">\n        <div class="text-center">\n          <div class="text-lg font-semibold mb-2">图表渲染错误</div>\n          <div class="text-sm opacity-75">'.concat(error instanceof Error ? error.message : '未知错误', "</div>\n        </div>\n      </div>\n    ");
        throw error;
    }
};
const validateMermaidSyntax = (code)=>{
    try {
        // 简单的语法验证
        if (!code.trim()) {
            return {
                isValid: false,
                error: '代码不能为空'
            };
        }
        // 检查是否包含基本的图表类型关键词
        const diagramTypes = [
            'flowchart',
            'graph',
            'sequenceDiagram',
            'classDiagram',
            'stateDiagram',
            'erDiagram',
            'journey',
            'gantt',
            'pie'
        ];
        const hasValidType = diagramTypes.some((type)=>code.toLowerCase().includes(type.toLowerCase()));
        if (!hasValidType) {
            return {
                isValid: false,
                error: '请确保代码包含有效的图表类型（如 flowchart, sequenceDiagram 等）'
            };
        }
        return {
            isValid: true
        };
    } catch (error) {
        return {
            isValid: false,
            error: error instanceof Error ? error.message : '语法验证失败'
        };
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Preview
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$lib$2f$mermaid$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/lib/mermaid-config.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function Preview(param) {
    let { code, onRenderComplete, onRenderError } = param;
    _s();
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [lastRenderedCode, setLastRenderedCode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // 防抖渲染函数
    const debouncedRender = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debounce"])({
        "Preview.useCallback[debouncedRender]": async (codeToRender)=>{
            if (!containerRef.current || !codeToRender.trim()) {
                return;
            }
            // 如果代码没有变化，跳过渲染
            if (codeToRender === lastRenderedCode) {
                return;
            }
            setIsLoading(true);
            setError(null);
            try {
                // 初始化 Mermaid
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$lib$2f$mermaid$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initializeMermaid"])(theme === 'dark');
                // 生成唯一ID
                const diagramId = "mermaid-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9));
                // 渲染图表
                const { svg } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$lib$2f$mermaid$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderMermaid"])(containerRef.current, codeToRender, diagramId);
                setLastRenderedCode(codeToRender);
                onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete(svg);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : '渲染失败';
                setError(errorMessage);
                onRenderError === null || onRenderError === void 0 ? void 0 : onRenderError(errorMessage);
                // 显示错误信息
                if (containerRef.current) {
                    containerRef.current.innerHTML = '\n            <div class="flex items-center justify-center h-full">\n              <div class="text-center p-6 max-w-md">\n                <div class="text-red-500 mb-4">\n                  <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>\n                  </svg>\n                </div>\n                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">图表渲染错误</h3>\n                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">'.concat(errorMessage, '</p>\n                <div class="text-xs text-gray-500 dark:text-gray-500">\n                  请检查 Mermaid 语法是否正确\n                </div>\n              </div>\n            </div>\n          ');
                }
            } finally{
                setIsLoading(false);
            }
        }
    }["Preview.useCallback[debouncedRender]"], 300), [
        theme,
        lastRenderedCode,
        onRenderComplete,
        onRenderError
    ]);
    // 当代码或主题变化时重新渲染
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Preview.useEffect": ()=>{
            if (code.trim()) {
                debouncedRender(code);
            } else {
                // 清空预览区域
                if (containerRef.current) {
                    containerRef.current.innerHTML = '\n          <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">\n            <div class="text-center">\n              <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>\n              </svg>\n              <p class="text-lg font-medium">开始编写 Mermaid 代码</p>\n              <p class="text-sm opacity-75 mt-1">图表将在这里实时显示</p>\n            </div>\n          </div>\n        ';
                }
                setLastRenderedCode('');
            }
        }
    }["Preview.useEffect"], [
        code,
        debouncedRender
    ]);
    // 主题变化时重新渲染
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Preview.useEffect": ()=>{
            if (lastRenderedCode) {
                setLastRenderedCode(''); // 强制重新渲染
                debouncedRender(code);
            }
        }
    }["Preview.useEffect"], [
        theme
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "h-full border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 relative overflow-hidden",
        children: [
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-4 right-4 z-10",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-3 py-2 rounded-lg shadow-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"
                        }, void 0, false, {
                            fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                            lineNumber: 116,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm font-medium",
                            children: "渲染中..."
                        }, void 0, false, {
                            fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                    lineNumber: 115,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: containerRef,
                className: "h-full w-full p-4 overflow-auto",
                style: {
                    // 确保 SVG 能够正确显示
                    minHeight: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }
            }, void 0, false, {
                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-4 right-4 flex space-x-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            const container = containerRef.current;
                            if (container) {
                                const svg = container.querySelector('svg');
                                if (svg) {
                                    var _svg_style_transform_match, _svg_style_transform;
                                    const currentScale = parseFloat(((_svg_style_transform = svg.style.transform) === null || _svg_style_transform === void 0 ? void 0 : (_svg_style_transform_match = _svg_style_transform.match(/scale\(([\d.]+)\)/)) === null || _svg_style_transform_match === void 0 ? void 0 : _svg_style_transform_match[1]) || '1');
                                    const newScale = Math.max(0.1, currentScale - 0.1);
                                    svg.style.transform = "scale(".concat(newScale, ")");
                                    svg.style.transformOrigin = 'center';
                                }
                            }
                        },
                        className: "p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                        title: "缩小",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-4 h-4",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M20 12H4"
                            }, void 0, false, {
                                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                        lineNumber: 137,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            const container = containerRef.current;
                            if (container) {
                                const svg = container.querySelector('svg');
                                if (svg) {
                                    svg.style.transform = 'scale(1)';
                                    svg.style.transformOrigin = 'center';
                                }
                            }
                        },
                        className: "p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                        title: "重置缩放",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-4 h-4",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                            }, void 0, false, {
                                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                                lineNumber: 173,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                            lineNumber: 172,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                        lineNumber: 158,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            const container = containerRef.current;
                            if (container) {
                                const svg = container.querySelector('svg');
                                if (svg) {
                                    var _svg_style_transform_match, _svg_style_transform;
                                    const currentScale = parseFloat(((_svg_style_transform = svg.style.transform) === null || _svg_style_transform === void 0 ? void 0 : (_svg_style_transform_match = _svg_style_transform.match(/scale\(([\d.]+)\)/)) === null || _svg_style_transform_match === void 0 ? void 0 : _svg_style_transform_match[1]) || '1');
                                    const newScale = Math.min(3, currentScale + 0.1);
                                    svg.style.transform = "scale(".concat(newScale, ")");
                                    svg.style.transformOrigin = 'center';
                                }
                            }
                        },
                        className: "p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                        title: "放大",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-4 h-4",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
                            }, void 0, false, {
                                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                                lineNumber: 194,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                            lineNumber: 193,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                        lineNumber: 177,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
}
_s(Preview, "pKl5QlsNmSPxfxBTUzWH+khNZmA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$out_web$2f$mermaid_online_cc$2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Preview;
var _c;
__turbopack_context__.k.register(_c, "Preview");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx [app-client] (ecmascript, next/dynamic entry)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx [app-client] (ecmascript)"));
}),
]);

//# sourceMappingURL=Desktop_out_web_mermaid_online_cc_f7403bd4._.js.map