{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/components/Editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { Editor as MonacoEditor } from '@monaco-editor/react';\nimport { useTheme } from 'next-themes';\n\ninterface EditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onValidationChange?: (isValid: boolean, errors: string[]) => void;\n}\n\nexport default function Editor({ value, onChange, onValidationChange }: EditorProps) {\n  const { theme } = useTheme();\n  const editorRef = useRef<any>(null);\n\n  const handleEditorDidMount = (editor: any, monaco: any) => {\n    editorRef.current = editor;\n\n    // 注册 Mermaid 语言支持\n    monaco.languages.register({ id: 'mermaid' });\n\n    // 设置 Mermaid 语法高亮\n    monaco.languages.setMonarchTokensProvider('mermaid', {\n      tokenizer: {\n        root: [\n          // 图表类型关键词\n          [/\\b(flowchart|graph|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie)\\b/, 'keyword'],\n          \n          // 方向关键词\n          [/\\b(TD|TB|BT|RL|LR)\\b/, 'keyword'],\n          \n          // 箭头和连接符\n          [/-->|---|\\-\\.\\->|\\-\\-\\>|\\=\\=\\>|\\-\\-\\-\\>/, 'operator'],\n          \n          // 节点形状\n          [/\\[.*?\\]|\\(.*?\\)|\\{.*?\\}|\\(\\(.*?\\)\\)|\\>.*?\\]|\\[.*?\\>/, 'string'],\n          \n          // 参与者和角色\n          [/participant|actor|note|loop|alt|else|opt|par|and|critical|break|ref/, 'keyword'],\n          \n          // 类图关键词\n          [/class|interface|enum|abstract|static|public|private|protected/, 'keyword'],\n          \n          // 状态图关键词\n          [/state|choice|fork|join|end/, 'keyword'],\n          \n          // 甘特图关键词\n          [/title|dateFormat|section|done|active|crit/, 'keyword'],\n          \n          // 注释\n          [/%%.*$/, 'comment'],\n          \n          // 字符串\n          [/\".*?\"/, 'string'],\n          [/'.*?'/, 'string'],\n          \n          // 数字\n          [/\\d+/, 'number'],\n          \n          // 标识符\n          [/[a-zA-Z_]\\w*/, 'identifier'],\n        ],\n      },\n    });\n\n    // 设置主题\n    monaco.editor.defineTheme('mermaid-light', {\n      base: 'vs',\n      inherit: true,\n      rules: [\n        { token: 'keyword', foreground: '0066cc', fontStyle: 'bold' },\n        { token: 'operator', foreground: '666666' },\n        { token: 'string', foreground: '008000' },\n        { token: 'comment', foreground: '999999', fontStyle: 'italic' },\n        { token: 'number', foreground: 'ff6600' },\n        { token: 'identifier', foreground: '333333' },\n      ],\n      colors: {\n        'editor.background': '#ffffff',\n        'editor.foreground': '#333333',\n        'editorLineNumber.foreground': '#999999',\n        'editor.selectionBackground': '#e6f3ff',\n        'editor.lineHighlightBackground': '#f5f5f5',\n      },\n    });\n\n    monaco.editor.defineTheme('mermaid-dark', {\n      base: 'vs-dark',\n      inherit: true,\n      rules: [\n        { token: 'keyword', foreground: '569cd6', fontStyle: 'bold' },\n        { token: 'operator', foreground: 'cccccc' },\n        { token: 'string', foreground: 'ce9178' },\n        { token: 'comment', foreground: '6a9955', fontStyle: 'italic' },\n        { token: 'number', foreground: 'b5cea8' },\n        { token: 'identifier', foreground: 'd4d4d4' },\n      ],\n      colors: {\n        'editor.background': '#1e1e1e',\n        'editor.foreground': '#d4d4d4',\n        'editorLineNumber.foreground': '#858585',\n        'editor.selectionBackground': '#264f78',\n        'editor.lineHighlightBackground': '#2a2d2e',\n      },\n    });\n\n    // 设置编辑器配置\n    editor.updateOptions({\n      fontSize: 14,\n      lineHeight: 20,\n      fontFamily: 'JetBrains Mono, Consolas, Monaco, \"Courier New\", monospace',\n      minimap: { enabled: false },\n      scrollBeyondLastLine: false,\n      wordWrap: 'on',\n      lineNumbers: 'on',\n      folding: true,\n      automaticLayout: true,\n      tabSize: 2,\n      insertSpaces: true,\n      renderWhitespace: 'selection',\n      bracketPairColorization: { enabled: true },\n    });\n\n    // 添加快捷键\n    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {\n      // 阻止默认保存行为\n      console.log('Save shortcut pressed');\n    });\n\n    // 监听内容变化进行简单验证\n    editor.onDidChangeModelContent(() => {\n      const content = editor.getValue();\n      validateContent(content);\n    });\n  };\n\n  const validateContent = (content: string) => {\n    const errors: string[] = [];\n    let isValid = true;\n\n    // 简单的语法验证\n    if (!content.trim()) {\n      errors.push('代码不能为空');\n      isValid = false;\n    } else {\n      // 检查是否包含图表类型\n      const diagramTypes = [\n        'flowchart', 'graph', 'sequenceDiagram', 'classDiagram', \n        'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie'\n      ];\n      \n      const hasValidType = diagramTypes.some(type => \n        content.toLowerCase().includes(type.toLowerCase())\n      );\n      \n      if (!hasValidType) {\n        errors.push('请确保代码包含有效的图表类型');\n        isValid = false;\n      }\n    }\n\n    onValidationChange?.(isValid, errors);\n  };\n\n  useEffect(() => {\n    if (editorRef.current) {\n      const monaco = (window as any).monaco;\n      if (monaco) {\n        monaco.editor.setTheme(theme === 'dark' ? 'mermaid-dark' : 'mermaid-light');\n      }\n    }\n  }, [theme]);\n\n  return (\n    <div className=\"h-full border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden\">\n      <MonacoEditor\n        height=\"100%\"\n        language=\"mermaid\"\n        value={value}\n        onChange={(value) => onChange(value || '')}\n        onMount={handleEditorDidMount}\n        theme={theme === 'dark' ? 'mermaid-dark' : 'mermaid-light'}\n        options={{\n          selectOnLineNumbers: true,\n          automaticLayout: true,\n          scrollBeyondLastLine: false,\n          minimap: { enabled: false },\n          fontSize: 14,\n          lineHeight: 20,\n          fontFamily: 'JetBrains Mono, Consolas, Monaco, \"Courier New\", monospace',\n          wordWrap: 'on',\n          lineNumbers: 'on',\n          folding: true,\n          tabSize: 2,\n          insertSpaces: true,\n          renderWhitespace: 'selection',\n          bracketPairColorization: { enabled: true },\n        }}\n        loading={\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n          </div>\n        }\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAYe,SAAS,OAAO,KAAoD;QAApD,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAe,GAApD;;IAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,0MAAQ;IAC1B,MAAM,YAAY,IAAA,kNAAM,EAAM;IAE9B,MAAM,uBAAuB,CAAC,QAAa;QACzC,UAAU,OAAO,GAAG;QAEpB,kBAAkB;QAClB,OAAO,SAAS,CAAC,QAAQ,CAAC;YAAE,IAAI;QAAU;QAE1C,kBAAkB;QAClB,OAAO,SAAS,CAAC,wBAAwB,CAAC,WAAW;YACnD,WAAW;gBACT,MAAM;oBACJ,UAAU;oBACV;wBAAC;wBAA+F;qBAAU;oBAE1G,QAAQ;oBACR;wBAAC;wBAAwB;qBAAU;oBAEnC,SAAS;oBACT;wBAAC;wBAA0C;qBAAW;oBAEtD,OAAO;oBACP;wBAAC;wBAAuD;qBAAS;oBAEjE,SAAS;oBACT;wBAAC;wBAAuE;qBAAU;oBAElF,QAAQ;oBACR;wBAAC;wBAAiE;qBAAU;oBAE5E,SAAS;oBACT;wBAAC;wBAA8B;qBAAU;oBAEzC,SAAS;oBACT;wBAAC;wBAA6C;qBAAU;oBAExD,KAAK;oBACL;wBAAC;wBAAS;qBAAU;oBAEpB,MAAM;oBACN;wBAAC;wBAAS;qBAAS;oBACnB;wBAAC;wBAAS;qBAAS;oBAEnB,KAAK;oBACL;wBAAC;wBAAO;qBAAS;oBAEjB,MAAM;oBACN;wBAAC;wBAAgB;qBAAa;iBAC/B;YACH;QACF;QAEA,OAAO;QACP,OAAO,MAAM,CAAC,WAAW,CAAC,iBAAiB;YACzC,MAAM;YACN,SAAS;YACT,OAAO;gBACL;oBAAE,OAAO;oBAAW,YAAY;oBAAU,WAAW;gBAAO;gBAC5D;oBAAE,OAAO;oBAAY,YAAY;gBAAS;gBAC1C;oBAAE,OAAO;oBAAU,YAAY;gBAAS;gBACxC;oBAAE,OAAO;oBAAW,YAAY;oBAAU,WAAW;gBAAS;gBAC9D;oBAAE,OAAO;oBAAU,YAAY;gBAAS;gBACxC;oBAAE,OAAO;oBAAc,YAAY;gBAAS;aAC7C;YACD,QAAQ;gBACN,qBAAqB;gBACrB,qBAAqB;gBACrB,+BAA+B;gBAC/B,8BAA8B;gBAC9B,kCAAkC;YACpC;QACF;QAEA,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB;YACxC,MAAM;YACN,SAAS;YACT,OAAO;gBACL;oBAAE,OAAO;oBAAW,YAAY;oBAAU,WAAW;gBAAO;gBAC5D;oBAAE,OAAO;oBAAY,YAAY;gBAAS;gBAC1C;oBAAE,OAAO;oBAAU,YAAY;gBAAS;gBACxC;oBAAE,OAAO;oBAAW,YAAY;oBAAU,WAAW;gBAAS;gBAC9D;oBAAE,OAAO;oBAAU,YAAY;gBAAS;gBACxC;oBAAE,OAAO;oBAAc,YAAY;gBAAS;aAC7C;YACD,QAAQ;gBACN,qBAAqB;gBACrB,qBAAqB;gBACrB,+BAA+B;gBAC/B,8BAA8B;gBAC9B,kCAAkC;YACpC;QACF;QAEA,UAAU;QACV,OAAO,aAAa,CAAC;YACnB,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,SAAS;gBAAE,SAAS;YAAM;YAC1B,sBAAsB;YACtB,UAAU;YACV,aAAa;YACb,SAAS;YACT,iBAAiB;YACjB,SAAS;YACT,cAAc;YACd,kBAAkB;YAClB,yBAAyB;gBAAE,SAAS;YAAK;QAC3C;QAEA,QAAQ;QACR,OAAO,UAAU,CAAC,OAAO,MAAM,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,IAAI,EAAE;YAC7D,WAAW;YACX,QAAQ,GAAG,CAAC;QACd;QAEA,eAAe;QACf,OAAO,uBAAuB,CAAC;YAC7B,MAAM,UAAU,OAAO,QAAQ;YAC/B,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAmB,EAAE;QAC3B,IAAI,UAAU;QAEd,UAAU;QACV,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,OAAO,IAAI,CAAC;YACZ,UAAU;QACZ,OAAO;YACL,aAAa;YACb,MAAM,eAAe;gBACnB;gBAAa;gBAAS;gBAAmB;gBACzC;gBAAgB;gBAAa;gBAAW;gBAAS;aAClD;YAED,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,OACrC,QAAQ,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;YAGjD,IAAI,CAAC,cAAc;gBACjB,OAAO,IAAI,CAAC;gBACZ,UAAU;YACZ;QACF;QAEA,+BAAA,yCAAA,mBAAqB,SAAS;IAChC;IAEA,IAAA,qNAAS;4BAAC;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,SAAS,AAAC,OAAe,MAAM;gBACrC,IAAI,QAAQ;oBACV,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU,SAAS,iBAAiB;gBAC7D;YACF;QACF;2BAAG;QAAC;KAAM;IAEV,qBACE,wOAAC;QAAI,WAAU;kBACb,cAAA,wOAAC,qOAAY;YACX,QAAO;YACP,UAAS;YACT,OAAO;YACP,UAAU,CAAC,QAAU,SAAS,SAAS;YACvC,SAAS;YACT,OAAO,UAAU,SAAS,iBAAiB;YAC3C,SAAS;gBACP,qBAAqB;gBACrB,iBAAiB;gBACjB,sBAAsB;gBACtB,SAAS;oBAAE,SAAS;gBAAM;gBAC1B,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,aAAa;gBACb,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,kBAAkB;gBAClB,yBAAyB;oBAAE,SAAS;gBAAK;YAC3C;YACA,uBACE,wOAAC;gBAAI,WAAU;0BACb,cAAA,wOAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;AAM3B;GAnMwB;;QACJ,0MAAQ;;;KADJ", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACtC,IAAI,OAAO,KAAK;QACd,OAAO,cAAc,CAAC,KAAK,KAAK;YAC9B,OAAO;YACP,YAAY;YACZ,cAAc;YACd,UAAU;QACZ;IACF,OAAO;QACL,GAAG,CAAC,IAAI,GAAG;IACb;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,cAAc;IACrC,IAAI,OAAO,OAAO,IAAI,CAAC;IAEvB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAC3C,IAAI,gBAAgB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;YACxD,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAChE;QACA,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IACxB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAEpD,IAAI,IAAI,GAAG;YACT,QAAQ,OAAO,SAAS,MAAM,OAAO,CAAC,SAAU,GAAG;gBACjD,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;YAC1C;QACF,OAAO,IAAI,OAAO,yBAAyB,EAAE;YAC3C,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;QACnE,OAAO;YACL,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC3C,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;YAC7E;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,8BAA8B,MAAM,EAAE,QAAQ;IACrD,IAAI,UAAU,MAAM,OAAO,CAAC;IAC5B,IAAI,SAAS,CAAC;IACd,IAAI,aAAa,OAAO,IAAI,CAAC;IAC7B,IAAI,KAAK;IAET,IAAK,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACtC,MAAM,UAAU,CAAC,EAAE;QACnB,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;QAChC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,yBAAyB,MAAM,EAAE,QAAQ;IAChD,IAAI,UAAU,MAAM,OAAO,CAAC;IAE5B,IAAI,SAAS,8BAA8B,QAAQ;IAEnD,IAAI,KAAK;IAET,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,mBAAmB,OAAO,qBAAqB,CAAC;QAEpD,IAAK,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAC5C,MAAM,gBAAgB,CAAC,EAAE;YACzB,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;YAChC,IAAI,CAAC,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,MAAM;YAC9D,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,GAAG,EAAE,CAAC;IAC5B,OAAO,gBAAgB,QAAQ,sBAAsB,KAAK,MAAM,4BAA4B,KAAK,MAAM;AACzG;AAEA,SAAS,gBAAgB,GAAG;IAC1B,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO;AACjC;AAEA,SAAS,sBAAsB,GAAG,EAAE,CAAC;IACnC,IAAI,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,QAAQ,IAAI,OAAO,IAAI,GAAG;IACxE,IAAI,OAAO,EAAE;IACb,IAAI,KAAK;IACT,IAAI,KAAK;IACT,IAAI,KAAK;IAET,IAAI;QACF,IAAK,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,EAAE,EAAE,IAAI,GAAG,KAAK,KAAM;YAClF,KAAK,IAAI,CAAC,GAAG,KAAK;YAElB,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG;QAC9B;IACF,EAAE,OAAO,KAAK;QACZ,KAAK;QACL,KAAK;IACP,SAAU;QACR,IAAI;YACF,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,IAAI,MAAM,EAAE,CAAC,SAAS;QAC/C,SAAU;YACR,IAAI,IAAI,MAAM;QAChB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,4BAA4B,CAAC,EAAE,MAAM;IAC5C,IAAI,CAAC,GAAG;IACR,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;IACvD,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IACpD,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;IAC3D,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;IAClD,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;AAC3G;AAEA,SAAS,kBAAkB,GAAG,EAAE,GAAG;IACjC,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;IAErD,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAErE,OAAO;AACT;AAEA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/state-local/lib/es/state-local.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACtC,IAAI,OAAO,KAAK;QACd,OAAO,cAAc,CAAC,KAAK,KAAK;YAC9B,OAAO;YACP,YAAY;YACZ,cAAc;YACd,UAAU;QACZ;IACF,OAAO;QACL,GAAG,CAAC,IAAI,GAAG;IACb;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,MAAM,EAAE,cAAc;IACrC,IAAI,OAAO,OAAO,IAAI,CAAC;IAEvB,IAAI,OAAO,qBAAqB,EAAE;QAChC,IAAI,UAAU,OAAO,qBAAqB,CAAC;QAC3C,IAAI,gBAAgB,UAAU,QAAQ,MAAM,CAAC,SAAU,GAAG;YACxD,OAAO,OAAO,wBAAwB,CAAC,QAAQ,KAAK,UAAU;QAChE;QACA,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;IACxB;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,MAAM;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE,IAAI,OAAO,SAAS,CAAC,EAAE,GAAG,CAAC;QAEpD,IAAI,IAAI,GAAG;YACT,QAAQ,OAAO,SAAS,MAAM,OAAO,CAAC,SAAU,GAAG;gBACjD,gBAAgB,QAAQ,KAAK,MAAM,CAAC,IAAI;YAC1C;QACF,OAAO,IAAI,OAAO,yBAAyB,EAAE;YAC3C,OAAO,gBAAgB,CAAC,QAAQ,OAAO,yBAAyB,CAAC;QACnE,OAAO;YACL,QAAQ,OAAO,SAAS,OAAO,CAAC,SAAU,GAAG;gBAC3C,OAAO,cAAc,CAAC,QAAQ,KAAK,OAAO,wBAAwB,CAAC,QAAQ;YAC7E;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS;IACP,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,MAAM,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACtF,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC7B;IAEA,OAAO,SAAU,CAAC;QAChB,OAAO,IAAI,WAAW,CAAC,SAAU,CAAC,EAAE,CAAC;YACnC,OAAO,EAAE;QACX,GAAG;IACL;AACF;AAEA,SAAS,MAAM,EAAE;IACf,OAAO,SAAS;QACd,IAAI,QAAQ,IAAI;QAEhB,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QAEA,OAAO,KAAK,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ;YACvD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBACjG,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YACpC;YAEA,OAAO,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM;QAC9C;IACF;AACF;AAEA,SAAS,SAAS,KAAK;IACrB,OAAO,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AAC1C;AAEA,SAAS,QAAQ,GAAG;IAClB,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,MAAM;AACjC;AAEA,SAAS,WAAW,KAAK;IACvB,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ;AACtD;AAEA,SAAS,gBAAgB,OAAO,EAAE,OAAO;IACvC,IAAI,CAAC,SAAS,UAAU,aAAa;IACrC,IAAI,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,SAAU,KAAK;QAC3C,OAAO,CAAC,eAAe,SAAS;IAClC,IAAI,aAAa;IACjB,OAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ;IAChC,IAAI,CAAC,WAAW,WAAW,aAAa;AAC1C;AAEA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,CAAC,CAAC,WAAW,YAAY,SAAS,QAAQ,GAAG,aAAa;IAC9D,IAAI,SAAS,YAAY,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,SAAU,QAAQ;QACrE,OAAO,CAAC,WAAW;IACrB,IAAI,aAAa;AACnB;AAEA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,CAAC,SAAS,aAAa;IAC3B,IAAI,CAAC,SAAS,UAAU,aAAa;IACrC,IAAI,QAAQ,UAAU,aAAa;AACrC;AAEA,SAAS,WAAW,aAAa,EAAE,IAAI;IACrC,MAAM,IAAI,MAAM,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,UAAU;AACjE;AAEA,IAAI,gBAAgB;IAClB,mBAAmB;IACnB,aAAa;IACb,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,cAAc;IACd,YAAY;IACZ,aAAa;IACb,WAAW;AACb;AACA,IAAI,eAAe,MAAM,YAAY;AACrC,IAAI,aAAa;IACf,SAAS;IACT,UAAU;IACV,SAAS;IACT,SAAS;AACX;AAEA,SAAS,OAAO,OAAO;IACrB,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IACnF,WAAW,OAAO,CAAC;IACnB,WAAW,OAAO,CAAC;IACnB,IAAI,QAAQ;QACV,SAAS;IACX;IACA,IAAI,YAAY,MAAM,gBAAgB,OAAO;IAC7C,IAAI,SAAS,MAAM,aAAa;IAChC,IAAI,WAAW,MAAM,WAAW,OAAO,EAAE;IACzC,IAAI,aAAa,MAAM,gBAAgB;IAEvC,SAAS;QACP,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,SAAU,KAAK;YAChG,OAAO;QACT;QACA,WAAW,QAAQ,CAAC;QACpB,OAAO,SAAS,MAAM,OAAO;IAC/B;IAEA,SAAS,SAAS,aAAa;QAC7B,QAAQ,WAAW,QAAQ,UAAU,YAAY;IACnD;IAEA,OAAO;QAAC;QAAU;KAAS;AAC7B;AAEA,SAAS,eAAe,KAAK,EAAE,aAAa;IAC1C,OAAO,WAAW,iBAAiB,cAAc,MAAM,OAAO,IAAI;AACpE;AAEA,SAAS,YAAY,KAAK,EAAE,OAAO;IACjC,MAAM,OAAO,GAAG,eAAe,eAAe,CAAC,GAAG,MAAM,OAAO,GAAG;IAClE,OAAO;AACT;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO,EAAE,OAAO;IAC7C,WAAW,WAAW,QAAQ,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,SAAU,KAAK;QACzF,IAAI;QAEJ,OAAO,CAAC,iBAAiB,OAAO,CAAC,MAAM,MAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,eAAe,IAAI,CAAC,SAAS,MAAM,OAAO,CAAC,MAAM;IAC7I;IACA,OAAO;AACT;AAEA,IAAI,QAAQ;IACV,QAAQ;AACV;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/config/index.js"], "sourcesContent": ["var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;;AAAA,IAAI,SAAS;IACX,OAAO;QACL,IAAI;IACN;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/utils/curry.js"], "sourcesContent": ["function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,EAAE;IACf,OAAO,SAAS;QACd,IAAI,QAAQ,IAAI;QAEhB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QAEA,OAAO,KAAK,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ;YACvD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBACjG,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YACpC;YAEA,OAAO,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM;QAC9C;IACF;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/utils/isObject.js"], "sourcesContent": ["function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n"], "names": [], "mappings": ";;;;AAAA,SAAS,SAAS,KAAK;IACrB,OAAO,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/validators/index.js"], "sourcesContent": ["import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEA;;;;CAIC,GAED,SAAS,eAAe,MAAM;IAC5B,IAAI,CAAC,QAAQ,aAAa;IAC1B,IAAI,CAAC,IAAA,uOAAQ,EAAC,SAAS,aAAa;IAEpC,IAAI,OAAO,IAAI,EAAE;QACf;QACA,OAAO;YACL,OAAO;gBACL,IAAI,OAAO,IAAI,CAAC,UAAU;YAC5B;QACF;IACF;IAEA,OAAO;AACT;AACA;;CAEC,GAGD,SAAS;IACP,QAAQ,IAAI,CAAC,cAAc,WAAW;AACxC;AAEA,SAAS,WAAW,aAAa,EAAE,IAAI;IACrC,MAAM,IAAI,MAAM,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,UAAU;AACjE;AAEA,IAAI,gBAAgB;IAClB,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,aAAa;AACf;AACA,IAAI,eAAe,IAAA,oOAAK,EAAC,YAAY;AACrC,IAAI,aAAa;IACf,QAAQ;AACV;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/utils/compose.js"], "sourcesContent": ["var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n"], "names": [], "mappings": ";;;;AAAA,IAAI,UAAU,SAAS;IACrB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,MAAM,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACtF,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAC7B;IAEA,OAAO,SAAU,CAAC;QAChB,OAAO,IAAI,WAAW,CAAC,SAAU,CAAC,EAAE,CAAC;YACnC,OAAO,EAAE;QACX,GAAG;IACL;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/utils/deepMerge.js"], "sourcesContent": ["import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,MAAM,MAAM,EAAE,MAAM;IAC3B,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;QACvC,IAAI,MAAM,CAAC,IAAI,YAAY,QAAQ;YACjC,IAAI,MAAM,CAAC,IAAI,EAAE;gBACf,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI;YAC3D;QACF;IACF;IACA,OAAO,IAAA,iQAAc,EAAC,IAAA,iQAAc,EAAC,CAAC,GAAG,SAAS;AACpD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/utils/makeCancelable.js"], "sourcesContent": ["// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n"], "names": [], "mappings": "AAAA,wGAAwG;;;;;;;AACxG,IAAI,sBAAsB;IACxB,MAAM;IACN,KAAK;AACP;AAEA,SAAS,eAAe,OAAO;IAC7B,IAAI,eAAe;IACnB,IAAI,iBAAiB,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QACxD,QAAQ,IAAI,CAAC,SAAU,GAAG;YACxB,OAAO,eAAe,OAAO,uBAAuB,QAAQ;QAC9D;QACA,OAAO,CAAC,QAAQ,CAAC;IACnB;IACA,OAAO,eAAe,MAAM,GAAG;QAC7B,OAAO,eAAe;IACxB,GAAG;AACL;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/loader/index.js"], "sourcesContent": ["import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,kCAAkC,GAElC,IAAI,gBAAgB,sNAAK,CAAC,MAAM,CAAC;IAC/B,QAAQ,qOAAQ;IAChB,eAAe;IACf,SAAS;IACT,QAAQ;IACR,QAAQ;AACV,IACI,iBAAiB,IAAA,iQAAc,EAAC,eAAe,IAC/C,WAAW,cAAc,CAAC,EAAE,EAC5B,WAAW,cAAc,CAAC,EAAE;AAChC;;;CAGC,GAGD,SAAS,OAAO,YAAY;IAC1B,IAAI,qBAAqB,yOAAU,CAAC,MAAM,CAAC,eACvC,SAAS,mBAAmB,MAAM,EAClC,SAAS,IAAA,2QAAwB,EAAC,oBAAoB;QAAC;KAAS;IAEpE,SAAS,SAAU,KAAK;QACtB,OAAO;YACL,QAAQ,IAAA,wOAAK,EAAC,MAAM,MAAM,EAAE;YAC5B,QAAQ;QACV;IACF;AACF;AACA;;;CAGC,GAGD,SAAS;IACP,IAAI,QAAQ,SAAS,SAAU,IAAI;QACjC,IAAI,SAAS,KAAK,MAAM,EACpB,gBAAgB,KAAK,aAAa,EAClC,UAAU,KAAK,OAAO;QAC1B,OAAO;YACL,QAAQ;YACR,eAAe;YACf,SAAS;QACX;IACF;IAEA,IAAI,CAAC,MAAM,aAAa,EAAE;QACxB,SAAS;YACP,eAAe;QACjB;QAEA,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,OAAO,CAAC,MAAM,MAAM;YAC1B,OAAO,IAAA,6OAAc,EAAC;QACxB;QAEA,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE;YACzC,oBAAoB,OAAO,MAAM;YACjC,MAAM,OAAO,CAAC,OAAO,MAAM;YAC3B,OAAO,IAAA,6OAAc,EAAC;QACxB;QAEA,IAAA,sOAAO,EAAC,eAAe,uBAAuB;IAChD;IAEA,OAAO,IAAA,6OAAc,EAAC;AACxB;AACA;;;;CAIC,GAGD,SAAS,cAAc,MAAM;IAC3B,OAAO,SAAS,IAAI,CAAC,WAAW,CAAC;AACnC;AACA;;;;CAIC,GAGD,SAAS,aAAa,GAAG;IACvB,IAAI,SAAS,SAAS,aAAa,CAAC;IACpC,OAAO,OAAO,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG;AACpC;AACA;;;CAGC,GAGD,SAAS,sBAAsB,eAAe;IAC5C,IAAI,QAAQ,SAAS,SAAU,KAAK;QAClC,IAAI,SAAS,MAAM,MAAM,EACrB,SAAS,MAAM,MAAM;QACzB,OAAO;YACL,QAAQ;YACR,QAAQ;QACV;IACF;IACA,IAAI,eAAe,aAAa,GAAG,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;IAEjE,aAAa,MAAM,GAAG;QACpB,OAAO;IACT;IAEA,aAAa,OAAO,GAAG,MAAM,MAAM;IACnC,OAAO;AACT;AACA;;CAEC,GAGD,SAAS;IACP,IAAI,QAAQ,SAAS,SAAU,KAAK;QAClC,IAAI,SAAS,MAAM,MAAM,EACrB,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM;QACzB,OAAO;YACL,QAAQ;YACR,SAAS;YACT,QAAQ;QACV;IACF;IACA,IAAI,UAAU,OAAO,OAAO;IAE5B,QAAQ,MAAM,CAAC,MAAM,MAAM;IAE3B,QAAQ;QAAC;KAAwB,EAAE,SAAU,MAAM;QACjD,oBAAoB;QACpB,MAAM,OAAO,CAAC;IAChB,GAAG,SAAU,KAAK;QAChB,MAAM,MAAM,CAAC;IACf;AACF;AACA;;CAEC,GAGD,SAAS,oBAAoB,MAAM;IACjC,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,SAAS;YACP,QAAQ;QACV;IACF;AACF;AACA;;;;CAIC,GAGD,SAAS;IACP,OAAO,SAAS,SAAU,KAAK;QAC7B,IAAI,SAAS,MAAM,MAAM;QACzB,OAAO;IACT;AACF;AAEA,IAAI,iBAAiB,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;IACxD,OAAO,SAAS;QACd,SAAS;QACT,QAAQ;IACV;AACF;AACA,IAAI,SAAS;IACX,QAAQ;IACR,MAAM;IACN,qBAAqB;AACvB;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/loader/lib/es/index.js"], "sourcesContent": ["import loader from './loader/index.js';\nexport { default } from './loader/index.js';\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/DiffEditor/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/DiffEditor/DiffEditor.tsx", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/MonacoContainer/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/MonacoContainer/MonacoContainer.tsx", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/MonacoContainer/styles.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/Loading/Loading.tsx", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/Loading/styles.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/Loading/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/hooks/useMount/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/hooks/useUpdate/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/utils/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/hooks/useMonaco/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/Editor/index.ts", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/Editor/Editor.tsx", "file:///Users/<USER>/Desktop/out_web/mermaid_online_cc/node_modules/%40monaco-editor/react/src/hooks/usePrevious/index.ts"], "sourcesContent": ["import loader from '@monaco-editor/loader';\nexport { loader };\n\nimport DiffEditor from './DiffEditor';\nexport * from './DiffEditor/types';\nexport { DiffEditor };\n\nimport useMonaco from './hooks/useMonaco';\nexport { useMonaco };\n\nimport Editor from './Editor';\nexport * from './Editor/types';\nexport { Editor };\nexport default Editor;\n\n// Monaco\nimport type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';\nexport type Monaco = typeof monaco;\n\n// Default themes\nexport type Theme = 'vs-dark' | 'light';\n", "import { memo } from 'react';\n\nimport DiffEditor from './DiffEditor';\n\nexport * from './types';\n\nexport default memo(DiffEditor);\n", "'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport MonacoContainer from '../MonacoContainer';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type DiffEditorProps, type MonacoDiffEditor } from './types';\nimport { type Monaco } from '..';\n\nfunction DiffEditor({\n  original,\n  modified,\n  language,\n  originalLanguage,\n  modifiedLanguage,\n  originalModelPath,\n  modifiedModelPath,\n  keepCurrentOriginalModel = false,\n  keepCurrentModifiedModel = false,\n  theme = 'light',\n  loading = 'Loading...',\n  options = {},\n  height = '100%',\n  width = '100%',\n  className,\n  wrapperProps = {},\n  beforeMount = noop,\n  onMount = noop,\n}: DiffEditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const editorRef = useRef<MonacoDiffEditor | null>(null);\n  const monacoRef = useRef<Monaco | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const preventCreation = useRef(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const originalEditor = editorRef.current.getOriginalEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          original || '',\n          originalLanguage || language || 'text',\n          originalModelPath || '',\n        );\n\n        if (model !== originalEditor.getModel()) {\n          originalEditor.setModel(model);\n        }\n      }\n    },\n    [originalModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const modifiedEditor = editorRef.current.getModifiedEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          modified || '',\n          modifiedLanguage || language || 'text',\n          modifiedModelPath || '',\n        );\n\n        if (model !== modifiedEditor.getModel()) {\n          modifiedEditor.setModel(model);\n        }\n      }\n    },\n    [modifiedModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const modifiedEditor = editorRef.current!.getModifiedEditor();\n      if (modifiedEditor.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        modifiedEditor.setValue(modified || '');\n      } else {\n        if (modified !== modifiedEditor.getValue()) {\n          modifiedEditor.executeEdits('', [\n            {\n              range: modifiedEditor.getModel()!.getFullModelRange(),\n              text: modified || '',\n              forceMoveMarkers: true,\n            },\n          ]);\n\n          modifiedEditor.pushUndoStop();\n        }\n      }\n    },\n    [modified],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.getModel()?.original.setValue(original || '');\n    },\n    [original],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const { original, modified } = editorRef.current!.getModel()!;\n\n      monacoRef.current!.editor.setModelLanguage(original, originalLanguage || language || 'text');\n      monacoRef.current!.editor.setModelLanguage(modified, modifiedLanguage || language || 'text');\n    },\n    [language, originalLanguage, modifiedLanguage],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  const setModels = useCallback(() => {\n    if (!monacoRef.current) return;\n    beforeMountRef.current(monacoRef.current);\n    const originalModel = getOrCreateModel(\n      monacoRef.current,\n      original || '',\n      originalLanguage || language || 'text',\n      originalModelPath || '',\n    );\n\n    const modifiedModel = getOrCreateModel(\n      monacoRef.current,\n      modified || '',\n      modifiedLanguage || language || 'text',\n      modifiedModelPath || '',\n    );\n\n    editorRef.current?.setModel({\n      original: originalModel,\n      modified: modifiedModel,\n    });\n  }, [\n    language,\n    modified,\n    modifiedLanguage,\n    original,\n    originalLanguage,\n    originalModelPath,\n    modifiedModelPath,\n  ]);\n\n  const createEditor = useCallback(() => {\n    if (!preventCreation.current && containerRef.current) {\n      editorRef.current = monacoRef.current!.editor.createDiffEditor(containerRef.current, {\n        automaticLayout: true,\n        ...options,\n      });\n\n      setModels();\n\n      monacoRef.current?.editor.setTheme(theme);\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [options, theme, setModels]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  function disposeEditor() {\n    const models = editorRef.current?.getModel();\n\n    if (!keepCurrentOriginalModel) {\n      models?.original?.dispose();\n    }\n\n    if (!keepCurrentModifiedModel) {\n      models?.modified?.dispose();\n    }\n\n    editorRef.current?.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default DiffEditor;\n", "import { memo } from 'react';\n\nimport MonacoContainer from './MonacoContainer';\n\nexport default memo(MonacoContainer);\n", "import React from 'react';\n\nimport styles from './styles';\nimport Loading from '../Loading';\nimport { type ContainerProps } from './types';\n\n// ** forwardref render functions do not support proptypes or defaultprops **\n// one of the reasons why we use a separate prop for passing ref instead of using forwardref\n\nfunction MonacoContainer({\n  width,\n  height,\n  isEditorReady,\n  loading,\n  _ref,\n  className,\n  wrapperProps,\n}: ContainerProps) {\n  return (\n    <section style={{ ...styles.wrapper, width, height }} {...wrapperProps}>\n      {!isEditorReady && <Loading>{loading}</Loading>}\n      <div\n        ref={_ref}\n        style={{ ...styles.fullWidth, ...(!isEditorReady && styles.hide) }}\n        className={className}\n      />\n    </section>\n  );\n}\n\nexport default MonacoContainer;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  wrapper: {\n    display: 'flex',\n    position: 'relative',\n    textAlign: 'initial',\n  },\n  fullWidth: {\n    width: '100%',\n  },\n  hide: {\n    display: 'none',\n  },\n};\n\nexport default styles;\n", "import React, { type PropsWithChildren } from 'react';\n\nimport styles from './styles';\n\nfunction Loading({ children }: PropsWithChildren) {\n  return <div style={styles.container}>{children}</div>;\n}\n\nexport default Loading;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  container: {\n    display: 'flex',\n    height: '100%',\n    width: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n};\n\nexport default styles;\n", "import Loading from './Loading';\n\nexport default Loading;\n", "import { useEffect, type EffectCallback } from 'react';\n\nfunction useMount(effect: EffectCallback) {\n  useEffect(effect, []);\n}\n\nexport default useMount;\n", "import { useEffect, useRef, type DependencyList, type EffectCallback } from 'react';\n\nfunction useUpdate(effect: EffectCallback, deps: DependencyList, applyChanges = true) {\n  const isInitialMount = useRef(true);\n\n  useEffect(\n    isInitialMount.current || !applyChanges\n      ? () => {\n          isInitialMount.current = false;\n        }\n      : effect,\n    deps,\n  );\n}\n\nexport default useUpdate;\n", "import { type Monaco } from '..';\n\n/**\n * noop is a helper function that does nothing\n * @returns undefined\n */\nfunction noop() {\n  /** no-op */\n}\n\n/**\n * getOrCreateModel is a helper function that will return a model if it exists\n * or create a new model if it does not exist.\n * This is useful for when you want to create a model for a file that may or may not exist yet.\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was found or created\n */\nfunction getOrCreateModel(monaco: Monaco, value: string, language: string, path: string) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\n\n/**\n * getModel is a helper function that will return a model if it exists\n * or return undefined if it does not exist.\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model that was found or undefined\n */\nfunction getModel(monaco: Monaco, path: string) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\n\n/**\n * createModel is a helper function that will create a new model\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was created\n */\nfunction createModel(monaco: Monaco, value: string, language?: string, path?: string) {\n  return monaco.editor.createModel(\n    value,\n    language,\n    path ? createModelUri(monaco, path) : undefined,\n  );\n}\n\n/**\n * createModelUri is a helper function that will create a new model uri\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model uri that was created\n */\nfunction createModelUri(monaco: Monaco, path: string) {\n  return monaco.Uri.parse(path);\n}\n\nexport { noop, getOrCreateModel };\n", "import { useState } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport useMount from '../useMount';\n\nfunction useMonaco() {\n  const [monaco, setMonaco] = useState(loader.__getMonacoInstance());\n\n  useMount(() => {\n    let cancelable: ReturnType<typeof loader.init>;\n\n    if (!monaco) {\n      cancelable = loader.init();\n\n      cancelable.then((monaco) => {\n        setMonaco(monaco);\n      });\n    }\n\n    return () => cancelable?.cancel();\n  });\n\n  return monaco;\n}\n\nexport default useMonaco;\n", "import { memo } from 'react';\n\nimport Editor from './Editor';\n\nexport * from './types';\n\nexport default memo(Editor);\n", "'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport loader from '@monaco-editor/loader';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport usePrevious from '../hooks/usePrevious';\nimport { type IDisposable, type editor } from 'monaco-editor';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type EditorProps } from './types';\nimport { type Monaco } from '..';\nimport MonacoContainer from '../MonacoContainer';\n\nconst viewStates = new Map();\n\nfunction Editor({\n  defaultValue,\n  defaultLanguage,\n  defaultPath,\n  value,\n  language,\n  path,\n  /* === */\n  theme = 'light',\n  line,\n  loading = 'Loading...',\n  options = {},\n  overrideServices = {},\n  saveViewState = true,\n  keepCurrentModel = false,\n  /* === */\n  width = '100%',\n  height = '100%',\n  className,\n  wrapperProps = {},\n  /* === */\n  beforeMount = noop,\n  onMount = noop,\n  onChange,\n  onValidate = noop,\n}: EditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const monacoRef = useRef<Monaco | null>(null);\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const subscriptionRef = useRef<IDisposable>();\n  const valueRef = useRef(value);\n  const previousPath = usePrevious(path);\n  const preventCreation = useRef(false);\n  const preventTriggerChangeEvent = useRef<boolean>(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      const model = getOrCreateModel(\n        monacoRef.current!,\n        defaultValue || value || '',\n        defaultLanguage || language || '',\n        path || defaultPath || '',\n      );\n\n      if (model !== editorRef.current?.getModel()) {\n        if (saveViewState) viewStates.set(previousPath, editorRef.current?.saveViewState());\n        editorRef.current?.setModel(model);\n        if (saveViewState) editorRef.current?.restoreViewState(viewStates.get(path));\n      }\n    },\n    [path],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (!editorRef.current || value === undefined) return;\n      if (editorRef.current.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        editorRef.current.setValue(value);\n      } else if (value !== editorRef.current.getValue()) {\n        preventTriggerChangeEvent.current = true;\n        editorRef.current.executeEdits('', [\n          {\n            range: editorRef.current.getModel()!.getFullModelRange(),\n            text: value,\n            forceMoveMarkers: true,\n          },\n        ]);\n\n        editorRef.current.pushUndoStop();\n        preventTriggerChangeEvent.current = false;\n      }\n    },\n    [value],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const model = editorRef.current?.getModel();\n      if (model && language) monacoRef.current?.editor.setModelLanguage(model, language);\n    },\n    [language],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      // reason for undefined check: https://github.com/suren-atoyan/monaco-react/pull/188\n      if (line !== undefined) {\n        editorRef.current?.revealLine(line);\n      }\n    },\n    [line],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  const createEditor = useCallback(() => {\n    if (!containerRef.current || !monacoRef.current) return;\n    if (!preventCreation.current) {\n      beforeMountRef.current(monacoRef.current);\n      const autoCreatedModelPath = path || defaultPath;\n\n      const defaultModel = getOrCreateModel(\n        monacoRef.current,\n        value || defaultValue || '',\n        defaultLanguage || language || '',\n        autoCreatedModelPath || '',\n      );\n\n      editorRef.current = monacoRef.current?.editor.create(\n        containerRef.current,\n        {\n          model: defaultModel,\n          automaticLayout: true,\n          ...options,\n        },\n        overrideServices,\n      );\n\n      saveViewState && editorRef.current.restoreViewState(viewStates.get(autoCreatedModelPath));\n\n      monacoRef.current.editor.setTheme(theme);\n\n      if (line !== undefined) {\n        editorRef.current.revealLine(line);\n      }\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [\n    defaultValue,\n    defaultLanguage,\n    defaultPath,\n    value,\n    language,\n    path,\n    options,\n    overrideServices,\n    saveViewState,\n    theme,\n    line,\n  ]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  // subscription\n  // to avoid unnecessary updates (attach - dispose listener) in subscription\n  valueRef.current = value;\n\n  // onChange\n  useEffect(() => {\n    if (isEditorReady && onChange) {\n      subscriptionRef.current?.dispose();\n      subscriptionRef.current = editorRef.current?.onDidChangeModelContent((event) => {\n        if (!preventTriggerChangeEvent.current) {\n          onChange(editorRef.current!.getValue(), event);\n        }\n      });\n    }\n  }, [isEditorReady, onChange]);\n\n  // onValidate\n  useEffect(() => {\n    if (isEditorReady) {\n      const changeMarkersListener = monacoRef.current!.editor.onDidChangeMarkers((uris) => {\n        const editorUri = editorRef.current!.getModel()?.uri;\n\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.current!.editor.getModelMarkers({\n              resource: editorUri,\n            });\n            onValidate?.(markers);\n          }\n        }\n      });\n\n      return () => {\n        changeMarkersListener?.dispose();\n      };\n    }\n    return () => {\n      // eslint happy\n    };\n  }, [isEditorReady, onValidate]);\n\n  function disposeEditor() {\n    subscriptionRef.current?.dispose();\n\n    if (keepCurrentModel) {\n      saveViewState && viewStates.set(path, editorRef.current!.saveViewState());\n    } else {\n      editorRef.current!.getModel()?.dispose();\n    }\n\n    editorRef.current!.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default Editor;\n", "import { useEffect, useRef } from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n\nexport default usePrevious;\n"], "names": ["loader", "memo", "React", "useState", "useRef", "useCallback", "useEffect", "loader", "memo", "React", "styles", "styles_default", "React", "styles", "styles_default", "Loading", "children", "React", "styles_default", "Loading_default", "Loading_default", "MonacoContainer", "width", "height", "isEditorReady", "loading", "_ref", "className", "wrapperProps", "React", "styles_default", "Loading_default", "MonacoContainer_default", "MonacoContainer_default", "memo", "useEffect", "useMount", "effect", "useMount_default", "useEffect", "useRef", "useUpdate", "effect", "deps", "applyChanges", "isInitialMount", "useUpdate_default", "noop", "getOrCreateModel", "monaco", "value", "language", "path", "getModel", "createModel", "createModelUri", "DiffE<PERSON>or", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "keepCurrentModifiedModel", "theme", "loading", "options", "height", "width", "className", "wrapperProps", "beforeMount", "noop", "onMount", "isEditorReady", "setIsEditorReady", "useState", "isMonacoMounting", "setIsMonacoMounting", "editor<PERSON><PERSON>", "useRef", "monacoRef", "containerRef", "onMountRef", "beforeMountRef", "preventCreation", "useMount_default", "cancelable", "loader", "monaco", "error", "dispose<PERSON><PERSON><PERSON>", "useUpdate_default", "originalEditor", "model", "getOrCreateModel", "modifiedEditor", "setModels", "useCallback", "originalModel", "modifiedModel", "createEditor", "useEffect", "models", "React", "MonacoContainer_default", "DiffEditor_default", "DiffEditor_default", "memo", "useState", "loader", "useMonaco", "monaco", "setMonaco", "useState", "loader", "useMount_default", "cancelable", "useMonaco_default", "memo", "React", "useState", "useEffect", "useRef", "useCallback", "loader", "useEffect", "useRef", "usePrevious", "value", "ref", "usePrevious_default", "viewStates", "Editor", "defaultValue", "defaultLanguage", "defaultPath", "value", "language", "path", "theme", "line", "loading", "options", "overrideServices", "saveViewState", "keepCurrentModel", "width", "height", "className", "wrapperProps", "beforeMount", "noop", "onMount", "onChange", "onValidate", "isEditorReady", "setIsEditorReady", "useState", "isMonacoMounting", "setIsMonacoMounting", "monacoRef", "useRef", "editor<PERSON><PERSON>", "containerRef", "onMountRef", "beforeMountRef", "subscriptionRef", "valueRef", "previousPath", "usePrevious_default", "preventCreation", "preventTriggerChangeEvent", "useMount_default", "cancelable", "loader", "monaco", "error", "dispose<PERSON><PERSON><PERSON>", "useUpdate_default", "model", "getOrCreateModel", "createEditor", "useCallback", "autoCreatedModelPath", "defaultModel", "useEffect", "event", "changeMarkersListener", "uris", "editor<PERSON><PERSON>", "uri", "markers", "React", "MonacoContainer_default", "Editor_default", "Editor_default", "memo", "src_default", "Editor_default"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,OAAY;ACAnB,OAAS,QAAAC,OAAY,QCErB,OAAOC,IAAS,YAAAC,GAAU,UAAAC,EAAQ,eAAAC,GAAa,aAAAC,OAAiB,QAChE,OAAOC,OAAY,wBCHnB,OAAS,QAAAC,OAAY,QCArB,OAAOC,MAAW;;;;;;;ACElB,IAAMC,KAAwC;IAC5C,SAAS;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA,WAAW;QACT,OAAO;IACT;IACA,MAAM;QACJ,SAAS;IACX;AACF,GAEOC,IAAQD,GChBf,OAAOE,OAAuC;;ACE9C,IAAMC,KAAwC;IAC5C,WAAW;QACT,SAAS;QACT,QAAQ;QACR,OAAO;QACP,gBAAgB;QAChB,YAAY;IACd;AACF,GAEOC,IAAQD;ADRf,SAASE,QAAmB;UAAT,UAAAC,CAAS,EAAA,CAAsB,EAAjC;IACf,OAAOC,mNAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,OAAOC,EAAO,SAAA;IAAA,GAAYF,CAAS;AACjD;AAEA,IAAOG,IAAQJ;AENf,IAAOK,IAAQA;AJOf,SAASC,QAQT,CAAmB;UAPjB,OAAAC,CAAAA,EACA,QAAAC,CAAAA,EACA,eAAAC,CAAAA,EACA,SAAAC,CAAAA,EACA,MAAAC,CAAAA,EACA,WAAAC,CAAAA,EACA,cAAAC,CACF,EAAA,GARyB;IASvB,OACEC,mNAAAA,CAAA,aAAA,CAAC,WAAA;QAAQ,OAAO;YAAE,GAAGC,EAAO,OAAA;YAAS,OAAAR;YAAO,QAAAC;QAAO;QAAI,GAAGK,CAAAA;IAAAA,GACvD,CAACJ,KAAiBK,mNAAAA,CAAA,aAAA,CAACE,GAAA,MAASN,CAAQ,GACrCI,mNAAAA,CAAA,aAAA,CAAC,OAAA;QACC,KAAKH;QACL,OAAO;YAAE,GAAGI,EAAO,SAAA;YAAW,GAAI,CAACN,KAAiBM,EAAO;QAAM;QACjE,WAAWH;IAAAA,CACb,CACF;AAEJ;AAEA,IAAOK,KAAQX;AD1Bf,IAAOY,QAAQC,gNAAAA,EAAKD,EAAe,EMJnC,OAAS,aAAAE,OAAsC;;AAE/C,SAASC,GAASC,CAAAA,CAAwB;QACxCF,qNAAAA,EAAUE,GAAQ,CAAC,CAAC;AACtB;AAEA,IAAOC,IAAQF,GCNf,OAAS,aAAAG,GAAW,UAAAC,OAAwD;;AAE5E,SAASC,GAAUC,CAAAA,EAAwBC,CAAAA;QAAsBC,qEAAe,CAAA,EAAM;IACpF,IAAMC,IAAiBL,sNAAAA,EAAO,CAAA,CAAI;QAElCD,qNAAAA,EACEM,EAAe,OAAA,IAAW,CAACD,IACvB,IAAM;QACJC,EAAe,OAAA,GAAU,CAAA;IAC3B,IACAH,GACJC,CACF;AACF;AAEA,IAAOG,IAAQL;ACTf,SAASM,GAAO,EAEhB;AAYA,SAASC,EAAiBC,CAAAA,EAAgBC,CAAAA,EAAeC,CAAAA,EAAkBC,CAAAA,CAAc;IACvF,OAAOC,GAASJ,GAAQG,CAAI,KAAKE,GAAYL,GAAQC,GAAOC,GAAUC,CAAI;AAC5E;AASA,SAASC,GAASJ,CAAAA,EAAgBG,CAAAA,CAAc;IAC9C,OAAOH,EAAO,MAAA,CAAO,QAAA,CAASM,GAAeN,GAAQG,CAAI,CAAC;AAC5D;AAUA,SAASE,GAAYL,CAAAA,EAAgBC,CAAAA,EAAeC,CAAAA,EAAmBC,CAAAA,CAAe;IACpF,OAAOH,EAAO,MAAA,CAAO,WAAA,CACnBC,GACAC,GACAC,IAAOG,GAAeN,GAAQG,CAAI,IAAI,KAAA,CACxC;AACF;AAQA,SAASG,GAAeN,CAAAA,EAAgBG,CAAAA,CAAc;IACpD,OAAOH,EAAO,GAAA,CAAI,KAAA,CAAMG,CAAI;AAC9B;AT/CA,SAASI,QAmBT,CAAoB;QAnBA,EAClB,UAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,kBAAAC,CAAAA,EACA,kBAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,0BAAAC,IAA2B,CAAA,CAAA,EAC3B,0BAAAC,IAA2B,CAAA,CAAA,EAC3B,OAAAC,IAAQ,OAAA,EACR,SAAAC,IAAU,YAAA,EACV,SAAAC,IAAU,CAAC,CAAA,EACX,QAAAC,IAAS,MAAA,EACT,OAAAC,IAAQ,MAAA,EACR,WAAAC,CAAAA,EACA,cAAAC,IAAe,CAAC,CAAA,EAChB,aAAAC,IAAcC,CAAAA,EACd,SAAAC,IAAUD,CACZ,EAAA;IACE,IAAM,CAACE,GAAeC,CAAgB,CAAA,OAAIC,oNAAAA,EAAS,CAAA,CAAK,GAClD,CAACC,GAAkBC,CAAmB,CAAA,OAAIF,oNAAAA,EAAS,CAAA,CAAI,GACvDG,IAAYC,sNAAAA,EAAgC,IAAI,GAChDC,QAAYD,kNAAAA,EAAsB,IAAI,GACtCE,QAAeF,kNAAAA,EAAuB,IAAI,GAC1CG,QAAaH,kNAAAA,EAAOP,CAAO,GAC3BW,QAAiBJ,kNAAAA,EAAOT,CAAW,GACnCc,QAAkBL,kNAAAA,EAAO,CAAA,CAAK;IAEpCM,EAAS,IAAM;QACb,IAAMC,IAAaC,qOAAAA,CAAO,IAAA,CAAK;QAE/B,OAAAD,EACG,IAAA,EAAME,IAAAA,CAAYR,EAAU,OAAA,GAAUQ,CAAAA,KAAWX,EAAoB,CAAA,CAAK,CAAC,EAC3E,KAAA,EACEY,2CACCA,EAAO,IAAA,MAAS,iBAAiB,QAAQ,KAAA,CAAM,iCAAiCA,CAAK,CACzF,GAEK,IAAOX,EAAU,OAAA,GAAUY,EAAc,IAAIJ,EAAW,MAAA,CAAO;IACxE,CAAC,GAEDK,EACE,IAAM;QACJ,IAAIb,EAAU,OAAA,IAAWE,EAAU,OAAA,EAAS;YAC1C,IAAMY,IAAiBd,EAAU,OAAA,CAAQ,iBAAA,CAAkB,GACrDe,IAAQC,EACZd,EAAU,OAAA,EACV1B,KAAY,IACZG,KAAoBD,KAAY,QAChCG,KAAqB,EACvB;YAEIkC,MAAUD,EAAe,QAAA,CAAS,KACpCA,EAAe,QAAA,CAASC,CAAK;QAAA;IAGnC,GACA;QAAClC,CAAiB;KAAA,EAClBc,CACF,GAEAkB,EACE,IAAM;QACJ,IAAIb,EAAU,OAAA,IAAWE,EAAU,OAAA,EAAS;YAC1C,IAAMe,IAAiBjB,EAAU,OAAA,CAAQ,iBAAA,CAAkB,GACrDe,IAAQC,EACZd,EAAU,OAAA,EACVzB,KAAY,IACZG,KAAoBF,KAAY,QAChCI,KAAqB,EACvB;YAEIiC,MAAUE,EAAe,QAAA,CAAS,KACpCA,EAAe,QAAA,CAASF,CAAK;QAAA;IAGnC,GACA;QAACjC,CAAiB;KAAA,EAClBa,CACF,GAEAkB,EACE,IAAM;QACJ,IAAMI,IAAiBjB,EAAU,OAAA,CAAS,iBAAA,CAAkB;QACxDiB,EAAe,SAAA,CAAUf,EAAU,OAAA,CAAS,MAAA,CAAO,YAAA,CAAa,QAAQ,IAC1Ee,EAAe,QAAA,CAASxC,KAAY,EAAE,IAElCA,MAAawC,EAAe,QAAA,CAAS,KAAA,CACvCA,EAAe,YAAA,CAAa,IAAI;YAC9B;gBACE,OAAOA,EAAe,QAAA,CAAS,EAAG,iBAAA,CAAkB;gBACpD,MAAMxC,KAAY;gBAClB,kBAAkB,CAAA;YACpB,CACF;SAAC,GAEDwC,EAAe,YAAA,CAAa,CAAA;IAGlC,GACA;QAACxC,CAAQ;KAAA,EACTkB,CACF,GAEAkB,EACE,IAAM;;sBACJb,EAAU,OAAA,iFAAS,QAAA,CAAS,6EAAG,QAAA,CAAS,QAAA,CAASxB,KAAY,EAAE;IACjE,GACA;QAACA,CAAQ;KAAA,EACTmB,CACF,GAEAkB,EACE,IAAM;QACJ,IAAM,EAAE,UAAArC,CAAAA,EAAU,UAAAC,CAAS,EAAA,GAAIuB,EAAU,OAAA,CAAS,QAAA,CAAS;QAE3DE,EAAU,OAAA,CAAS,MAAA,CAAO,gBAAA,CAAiB1B,GAAUG,KAAoBD,KAAY,MAAM,GAC3FwB,EAAU,OAAA,CAAS,MAAA,CAAO,gBAAA,CAAiBzB,GAAUG,KAAoBF,KAAY,MAAM;IAC7F,GACA;QAACA;QAAUC;QAAkBC,CAAgB;KAAA,EAC7Ce,CACF,GAEAkB,EACE,IAAM;;wBACM,OAAA,+CAAVX,WAAmB,MAAA,CAAO,QAAA,CAASjB,CAAK;IAC1C,GACA;QAACA,CAAK;KAAA,EACNU,CACF,GAEAkB,EACE,IAAM;YACJb;wBAAU,OAAA,0DAAS,aAAA,CAAcb,CAAO;IAC1C,GACA;QAACA,CAAO;KAAA,EACRQ,CACF;IAEA,IAAMuB,QAAYC,uNAAAA,EAAY,IAAM;YAiBlCnB;QAhBA,IAAI,CAACE,EAAU,OAAA,EAAS;QACxBG,EAAe,OAAA,CAAQH,EAAU,OAAO;QACxC,IAAMkB,IAAgBJ,EACpBd,EAAU,OAAA,EACV1B,KAAY,IACZG,KAAoBD,KAAY,QAChCG,KAAqB,EACvB,GAEMwC,IAAgBL,EACpBd,EAAU,OAAA,EACVzB,KAAY,IACZG,KAAoBF,KAAY,QAChCI,KAAqB,EACvB;wBAEU,OAAA,0DAAS,QAAA,CAAS;YAC1B,UAAUsC;YACV,UAAUC;QACZ,CAAC;IACH,GAAG;QACD3C;QACAD;QACAG;QACAJ;QACAG;QACAE;QACAC,CACF;KAAC,GAEKwC,QAAeH,uNAAAA,EAAY,IAAM;YASnCjB;QARE,CAACI,EAAgB,OAAA,IAAWH,EAAa,OAAA,IAAA,CAC3CH,EAAU,OAAA,GAAUE,EAAU,OAAA,CAAS,MAAA,CAAO,gBAAA,CAAiBC,EAAa,OAAA,EAAS;YACnF,iBAAiB,CAAA;YACjB,GAAGhB,CACL,CAAC;YAED+B,EAAU,mBAEA,OAAA,0DAAS,MAAA,CAAO,QAAA,CAASjC,CAAK,GAExCW,EAAiB,CAAA,CAAI,GACrBU,EAAgB,OAAA,GAAU,CAAA,CAAA;IAE9B,GAAG;QAACnB;QAASF;QAAOiC,CAAS;KAAC;IAE9BK,yNAAAA,EAAU,IAAM;QACV5B,KACFS,EAAW,OAAA,CAAQJ,EAAU,OAAA,EAAUE,EAAU,OAAQ;IAE7D,GAAG;QAACP,CAAa;KAAC,OAElB4B,qNAAAA,EAAU,IAAM;QACd,CAACzB,KAAoB,CAACH,KAAiB2B,EAAa;IACtD,GAAG;QAACxB;QAAkBH;QAAe2B,CAAY;KAAC;IAElD,SAASV,GAAgB;kDAWvBZ;QAVA,IAAMwB,oBAAmB,OAAA,+CAAVxB,WAAmB,QAAA,CAAS;QAEtCjB,6DACK,QAAA,gDAARyC,YAAkB,OAAA,CAAQ,IAGvBxC,MACHwC,uDAAQ,QAAA,4DAAU,OAAA,CAAQ,qBAGlB,OAAA,4DAAS,OAAA,CAAQ;IAC7B;IAEA,OACEC,mNAAAA,CAAA,aAAA,CAACC,GAAA;QACC,OAAOrC;QACP,QAAQD;QACR,eAAeO;QACf,SAAST;QACT,MAAMiB;QACN,WAAWb;QACX,cAAcC;IAAAA,CAChB;AAEJ;AAEA,IAAOoC,KAAQpD;ADtOf,IAAOqD,SAAQC,gNAAAA,EAAKD,EAAU,EWN9B,OAAS,YAAAE,OAAgB,QACzB,OAAOC,OAAY;;;AAInB,SAASC,IAAY;IACnB,IAAM,CAACC,GAAQC,CAAS,CAAA,OAAIC,oNAAAA,EAASC,qOAAAA,CAAO,mBAAA,CAAoB,CAAC;IAEjE,OAAAC,EAAS,IAAM;QACb,IAAIC;QAEJ,OAAKL,KAAAA,CACHK,IAAaF,qOAAAA,CAAO,IAAA,CAAK,GAEzBE,EAAW,IAAA,EAAML,GAAW;YAC1BC,EAAUD,CAAM;QAClB,CAAC,CAAA,GAGI,0CAAMK,EAAY,MAAA,CAAO;IAClC,CAAC,GAEML;AACT;AAEA,IAAOM,KAAQP,GCzBf,OAAS,QAAAQ,OAAY,QCErB,OAAOC,IAAS,YAAAC,GAAU,aAAAC,EAAW,UAAAC,EAAQ,eAAAC,OAAmB,QAChE,OAAOC,OAAY,wBCHnB,OAAS,aAAAC,GAAW,UAAAC,OAAc;;;;;AAElC,SAASC,GAAeC,CAAAA,CAAU;IAChC,IAAMC,QAAMH,kNAAAA,CAAU;IAEtB,WAAAD,qNAAAA,EAAU,IAAM;QACdI,EAAI,OAAA,GAAUD;IAChB,GAAG;QAACA,CAAK;KAAC,GAEHC,EAAI;AACb;AAEA,IAAOC,KAAQH;ADCf,IAAMI,IAAa,IAAI;AAEvB,SAASC,QAyBT,CAAgB;UAxBd,cAAAC,CAAAA,EACA,iBAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,MAAAC,CAAAA,EAEA,OAAAC,IAAQ,OAAA,EACR,MAAAC,CAAAA,EACA,SAAAC,IAAU,YAAA,EACV,SAAAC,IAAU,CAAC,CAAA,EACX,kBAAAC,IAAmB,CAAC,CAAA,EACpB,eAAAC,IAAgB,CAAA,CAAA,EAChB,kBAAAC,IAAmB,CAAA,CAAA,EAEnB,OAAAC,IAAQ,MAAA,EACR,QAAAC,IAAS,MAAA,EACT,WAAAC,CAAAA,EACA,cAAAC,IAAe,CAAC,CAAA,EAEhB,aAAAC,IAAcC,CAAAA,EACd,SAAAC,IAAUD,CAAAA,EACV,UAAAE,CAAAA,EACA,YAAAC,IAAaH,CACf,EAAA,GAzBgB;IA0Bd,IAAM,CAACI,GAAeC,CAAgB,CAAA,OAAIC,oNAAAA,EAAS,CAAA,CAAK,GAClD,CAACC,GAAkBC,CAAmB,CAAA,OAAIF,oNAAAA,EAAS,CAAA,CAAI,GACvDG,QAAYC,kNAAAA,EAAsB,IAAI,GACtCC,QAAYD,kNAAAA,EAA4C,IAAI,GAC5DE,QAAeF,kNAAAA,EAAuB,IAAI,GAC1CG,IAAaH,sNAAAA,EAAOT,CAAO,GAC3Ba,QAAiBJ,kNAAAA,EAAOX,CAAW,GACnCgB,QAAkBL,kNAAAA,CAAoB,IACtCM,QAAWN,kNAAAA,EAAOzB,CAAK,GACvBgC,IAAeC,GAAY/B,CAAI,GAC/BgC,QAAkBT,kNAAAA,EAAO,CAAA,CAAK,GAC9BU,IAA4BV,sNAAAA,EAAgB,CAAA,CAAK;IAEvDW,EAAS,IAAM;QACb,IAAMC,IAAaC,qOAAAA,CAAO,IAAA,CAAK;QAE/B,OAAAD,EACG,IAAA,EAAME,IAAAA,CAAYf,EAAU,OAAA,GAAUe,CAAAA,KAAWhB,EAAoB,CAAA,CAAK,CAAC,EAC3E,KAAA,EACEiB,2CACCA,EAAO,IAAA,MAAS,iBAAiB,QAAQ,KAAA,CAAM,iCAAiCA,CAAK,CACzF,GAEK,IAAOd,EAAU,OAAA,GAAUe,GAAc,IAAIJ,EAAW,MAAA,CAAO;IACxE,CAAC,GAEDK,EACE,IAAM;YAQUhB,YACoCA,aAChDA;QATF,IAAMiB,IAAQC,EACZpB,EAAU,OAAA,EACV3B,KAAgBG,KAAS,IACzBF,KAAmBG,KAAY,IAC/BC,KAAQH,KAAe,EACzB;QAEI4C,uBAAoB,OAAA,0DAAS,QAAA,CAAS,MAAA,CACpCnC,KAAeb,EAAW,GAAA,CAAIqC,oBAAwB,OAAA,4DAAS,aAAA,CAAc,CAAC,oBACxE,OAAA,4DAAS,QAAA,CAASW,CAAK,GAC7BnC,OAAekB,gBAAU,OAAA,4DAAS,gBAAA,CAAiB/B,EAAW,GAAA,CAAIO,CAAI,CAAC,EAAA;IAE/E,GACA;QAACA,CAAI;KAAA,EACLiB,CACF,GAEAuB,EACE,IAAM;;wBACM,OAAA,+CAAVhB,WAAmB,aAAA,CAAcpB,CAAO;IAC1C,GACA;QAACA,CAAO;KAAA,EACRa,CACF,GAEAuB,EACE,IAAM;QACA,CAAChB,EAAU,OAAA,IAAW1B,MAAU,KAAA,KAAA,CAChC0B,EAAU,OAAA,CAAQ,SAAA,CAAUF,EAAU,OAAA,CAAS,MAAA,CAAO,YAAA,CAAa,QAAQ,IAC7EE,EAAU,OAAA,CAAQ,QAAA,CAAS1B,CAAK,IACvBA,MAAU0B,EAAU,OAAA,CAAQ,QAAA,CAAS,KAAA,CAC9CS,EAA0B,OAAA,GAAU,CAAA,GACpCT,EAAU,OAAA,CAAQ,YAAA,CAAa,IAAI;YACjC;gBACE,OAAOA,EAAU,OAAA,CAAQ,QAAA,CAAS,EAAG,iBAAA,CAAkB;gBACvD,MAAM1B;gBACN,kBAAkB,CAAA;YACpB,CACF;SAAC,GAED0B,EAAU,OAAA,CAAQ,YAAA,CAAa,GAC/BS,EAA0B,OAAA,GAAU,CAAA,CAAA,CAAA;IAExC,GACA;QAACnC,CAAK;KAAA,EACNmB,CACF,GAEAuB,EACE,IAAM;;QACJ,IAAMC,oBAAkB,OAAA,+CAAVjB,WAAmB,QAAA,CAAS;QACtCiB,KAAS1C,sBAAoB,OAAA,+CAAVuB,WAAmB,MAAA,CAAO,gBAAA,CAAiBmB,GAAO1C,CAAQ;IACnF,GACA;QAACA,CAAQ;KAAA,EACTkB,CACF,GAEAuB,EACE,IAAM;;QAEAtC,MAAS,KAAA,sBACD,OAAA,+CAAVsB,WAAmB,UAAA,CAAWtB,CAAI;IAEtC,GACA;QAACA,CAAI;KAAA,EACLe,CACF,GAEAuB,EACE,IAAM;;wBACM,OAAA,+CAAVlB,WAAmB,MAAA,CAAO,QAAA,CAASrB,CAAK;IAC1C,GACA;QAACA,CAAK;KAAA,EACNgB,CACF;IAEA,IAAM0B,QAAeC,uNAAAA,EAAY,IAAM;QACrC,IAAI,CAAA,CAAA,CAACnB,EAAa,OAAA,IAAW,CAACH,EAAU,OAAA,KACpC,CAACU,EAAgB,OAAA,EAAS;gBAWRV;YAVpBK,EAAe,OAAA,CAAQL,EAAU,OAAO;YACxC,IAAMuB,IAAuB7C,KAAQH,GAE/BiD,IAAeJ,EACnBpB,EAAU,OAAA,EACVxB,KAASH,KAAgB,IACzBC,KAAmBG,KAAY,IAC/B8C,KAAwB,EAC1B;YAEArB,EAAU,OAAA,mBAAoB,OAAA,0DAAS,MAAA,CAAO,MAAA,CAC5CC,EAAa,OAAA,EACb;gBACE,OAAOqB;gBACP,iBAAiB,CAAA;gBACjB,GAAG1C;YACL,GACAC,CACF,GAEAC,KAAiBkB,EAAU,OAAA,CAAQ,gBAAA,CAAiB/B,EAAW,GAAA,CAAIoD,CAAoB,CAAC,GAExFvB,EAAU,OAAA,CAAQ,MAAA,CAAO,QAAA,CAASrB,CAAK,GAEnCC,MAAS,KAAA,KACXsB,EAAU,OAAA,CAAQ,UAAA,CAAWtB,CAAI,GAGnCgB,EAAiB,CAAA,CAAI,GACrBc,EAAgB,OAAA,GAAU,CAAA;QAAA;IAE9B,GAAG;QACDrC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAI;QACAC;QACAC;QACAL;QACAC,CACF;KAAC;IAED6C,yNAAAA,EAAU,IAAM;QACV9B,KACFS,EAAW,OAAA,CAAQF,EAAU,OAAA,EAAUF,EAAU,OAAQ;IAE7D,GAAG;QAACL,CAAa;KAAC,OAElB8B,qNAAAA,EAAU,IAAM;QACd,CAAC3B,KAAoB,CAACH,KAAiB0B,EAAa;IACtD,GAAG;QAACvB;QAAkBH;QAAe0B,CAAY;KAAC,GAIlDd,EAAS,OAAA,GAAU/B,OAGnBiD,qNAAAA,EAAU,IAAM;wBAGcvB;QAFxBP,KAAiBF,KAAAA,iBACH,OAAA,+CAAhBa,WAAyB,OAAA,CAAQ,GACjCA,EAAgB,OAAA,mBAAoB,OAAA,0DAAS,uBAAA,EAAyBoB,GAAU;YACzEf,EAA0B,OAAA,IAC7BlB,EAASS,EAAU,OAAA,CAAS,QAAA,CAAS,GAAGwB,CAAK;QAEjD,CAAC,CAAA;IAEL,GAAG;QAAC/B;QAAeF,CAAQ;KAAC,GAG5BgC,yNAAAA,EAAU,IAAM;QACd,IAAI9B,GAAe;YACjB,IAAMgC,IAAwB3B,EAAU,OAAA,CAAS,MAAA,CAAO,kBAAA,EAAoB4B,GAAS;oBACjE1B;gBAAlB,IAAM2B,6BAAsB,OAAA,CAAS,QAAA,CAAS,6EAAG,GAAA;gBAEjD,IAAIA,KACoCD,EAAK,IAAA,EAAME,IAAQA,EAAI,IAAA,KAASD,EAAU,IAAI,GACjD;oBACjC,IAAME,IAAU/B,EAAU,OAAA,CAAS,MAAA,CAAO,eAAA,CAAgB;wBACxD,UAAU6B;oBACZ,CAAC;0DACDnC,EAAaqC,CAAO;gBAAA;YAG1B,CAAC;YAED,OAAO,IAAM;sDACXJ,EAAuB,OAAA,CAAQ;YACjC;QAAA;QAEF,OAAO,IAAM,CAEb;IACF,GAAG;QAAChC;QAAeD,CAAU;KAAC;IAE9B,SAASuB,IAAgB;wBAMrBf;QALFI,gBAAgB,OAAA,0DAAS,OAAA,CAAQ,GAE7BrB,IACFD,KAAiBb,EAAW,GAAA,CAAIO,GAAMwB,EAAU,OAAA,CAAS,aAAA,CAAc,CAAC,6BAE9D,OAAA,CAAS,QAAA,CAAS,6EAAG,OAAA,CAAQ,GAGzCA,EAAU,OAAA,CAAS,OAAA,CAAQ;IAC7B;IAEA,OACE8B,mNAAAA,CAAA,aAAA,CAACC,GAAA;QACC,OAAO/C;QACP,QAAQC;QACR,eAAeQ;QACf,SAASd;QACT,MAAMsB;QACN,WAAWf;QACX,cAAcC;IAAAA,CAChB;AAEJ;AAEA,IAAO6C,KAAQ9D;ADxQf,IAAO+D,SAAQC,gNAAAA,EAAKD,EAAM;AbO1B,IAAOE,KAAQC", "debugId": null}}]}