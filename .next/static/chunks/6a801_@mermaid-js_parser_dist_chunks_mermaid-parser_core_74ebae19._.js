(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/info-63CPKGFF.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_info-63CPKGFF_mjs_579b4b17._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_info-63CPKGFF_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/info-63CPKGFF.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/packet-HUATNLJX.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_packet-HUATNLJX_mjs_26e9b427._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_packet-HUATNLJX_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/packet-HUATNLJX.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/pie-WTHONI2E.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_pie-WTHONI2E_mjs_038c8ecd._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_pie-WTHONI2E_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/pie-WTHONI2E.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/architecture-O4VJ6CD3.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/5ed85_parser_dist_chunks_mermaid-parser_core_architecture-O4VJ6CD3_mjs_bbec2aa7._.js",
  "static/chunks/5ed85_parser_dist_chunks_mermaid-parser_core_architecture-O4VJ6CD3_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/architecture-O4VJ6CD3.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/gitGraph-ZV4HHKMB.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/5ed85_parser_dist_chunks_mermaid-parser_core_gitGraph-ZV4HHKMB_mjs_1669ea12._.js",
  "static/chunks/5ed85_parser_dist_chunks_mermaid-parser_core_gitGraph-ZV4HHKMB_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/gitGraph-ZV4HHKMB.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/radar-NJJJXTRR.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_radar-NJJJXTRR_mjs_e2021b9b._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_radar-NJJJXTRR_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/radar-NJJJXTRR.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/treemap-75Q7IDZK.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_treemap-75Q7IDZK_mjs_e6ef1169._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_treemap-75Q7IDZK_mjs_9275d321._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/treemap-75Q7IDZK.mjs [app-client] (ecmascript)");
    });
});
}),
]);