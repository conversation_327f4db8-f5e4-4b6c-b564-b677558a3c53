(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/Desktop/out_web/mermaid_online_cc/components/Editor.tsx [app-client] (ecmascript, next/dynamic entry, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/Desktop_out_web_mermaid_online_cc_10fcc7e5._.js",
  "static/chunks/Desktop_out_web_mermaid_online_cc_components_Editor_tsx_6df0f54f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/components/Editor.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx [app-client] (ecmascript, next/dynamic entry, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_5d51cf1d._.js",
  "static/chunks/6a801_mermaid_dist_976602b4._.js",
  "static/chunks/6a801_lodash-es_6ba82654._.js",
  "static/chunks/6a801_3ec99b53._.js",
  "static/chunks/Desktop_out_web_mermaid_online_cc_f7403bd4._.js",
  "static/chunks/Desktop_out_web_mermaid_online_cc_components_Preview_tsx_6df0f54f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/components/Preview.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
]);