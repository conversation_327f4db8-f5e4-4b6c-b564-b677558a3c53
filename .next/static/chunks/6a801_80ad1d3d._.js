(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/katex/dist/katex.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_katex_dist_katex_mjs_5cc3bbe8._.js",
  "static/chunks/6a801_katex_dist_katex_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/katex/dist/katex.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/dagre-6UL2VRFP.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_671266a8._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_dagre-6UL2VRFP_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/dagre-6UL2VRFP.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/cose-bilkent-S5V4N54A.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_cytoscape_dist_cytoscape_esm_mjs_853138a1._.js",
  "static/chunks/6a801_layout-base_layout-base_67a59167.js",
  "static/chunks/6a801_e3c71874._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_cose-bilkent-S5V4N54A_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/cose-bilkent-S5V4N54A.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-YG6GDRKO.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_dcf9d7bb._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_c4Diagram-YG6GDRKO_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-YG6GDRKO.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/flowDiagram-NV44I4VS.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_c05c9e50._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_flowDiagram-NV44I4VS_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/flowDiagram-NV44I4VS.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/erDiagram-Q2GNP2WA.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_ea48f52b._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_erDiagram-Q2GNP2WA_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/erDiagram-Q2GNP2WA.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/gitGraphDiagram-NY62KEGX.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_b20f52ce._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_a51564be._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_924f8dea._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_gitGraphDiagram-NY62KEGX_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/gitGraphDiagram-NY62KEGX.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-LVOFAZNH.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_bd271ddc._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_ganttDiagram-LVOFAZNH_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-LVOFAZNH.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-F6ZHWCRC.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_af803cb2._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_25d9dc96._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_infoDiagram-F6ZHWCRC_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-F6ZHWCRC.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-ADFJNKIX.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_9510d1ec._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_98a2a1bb._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_pieDiagram-ADFJNKIX_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-ADFJNKIX.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-AYHSOK5B.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_ad11bdea._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_quadrantDiagram-AYHSOK5B_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-AYHSOK5B.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-PRI3JC2R.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_d4f5d71e._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_xychartDiagram-PRI3JC2R_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-PRI3JC2R.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-UZGBJVZJ.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_89748789._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_requirementDiagram-UZGBJVZJ_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-UZGBJVZJ.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-WL72ISMW.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_74e0128f._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_sequenceDiagram-WL72ISMW_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-WL72ISMW.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-2ON5EDUG.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_fa36bec5._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_classDiagram-2ON5EDUG_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-2ON5EDUG.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-v2-WZHVMYZB.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_08604142._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_classDiagram-v2-WZHVMYZB_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-v2-WZHVMYZB.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-FKZM4ZOC.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_343d4668._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_stateDiagram-FKZM4ZOC_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-FKZM4ZOC.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4FDKWEC3.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_d04258b9._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_stateDiagram-v2-4FDKWEC3_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-4FDKWEC3.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-XKPGCS4Q.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_29a5597d._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_journeyDiagram-XKPGCS4Q_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/journeyDiagram-XKPGCS4Q.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-IT6M3QCI.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_2f007a29._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_timeline-definition-IT6M3QCI_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-IT6M3QCI.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/mindmap-definition-VGOIOE7T.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_b7389166._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_mindmap-definition-VGOIOE7T_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/mindmap-definition-VGOIOE7T.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-3W4ZIXB7.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_04b90af5._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_kanban-definition-3W4ZIXB7_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-3W4ZIXB7.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-TZEHDZUN.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_230321be._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_sankeyDiagram-TZEHDZUN_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-TZEHDZUN.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-S2PKOQOG.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_668ffe78._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_aea6bb7b._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_diagram-S2PKOQOG_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-S2PKOQOG.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-QEK2KX5R.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_d9135db2._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_391970be._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_diagram-QEK2KX5R_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-QEK2KX5R.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/blockDiagram-VD42YOAC.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_a9f7c5be._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_blockDiagram-VD42YOAC_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/blockDiagram-VD42YOAC.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/architectureDiagram-VXUJARFQ.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_601cdfb6._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_cytoscape_dist_cytoscape_esm_mjs_853138a1._.js",
  "static/chunks/a70de_layout-base_layout-base_7edd3739.js",
  "static/chunks/a70de_cose-base_cose-base_7db7028e.js",
  "static/chunks/6a801_bdd84b51._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_architectureDiagram-VXUJARFQ_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/architectureDiagram-VXUJARFQ.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-PSM6KHXK.mjs [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a801_@mermaid-js_parser_dist_chunks_mermaid-parser_core_890b2867._.js",
  "static/chunks/6a801_langium_lib_2bd8c4bb._.js",
  "static/chunks/6a801_chevrotain_lib_src_b8d24c19._.js",
  "static/chunks/6a801_lodash-es_acafb3b6._.js",
  "static/chunks/6a801_@mermaid-js_parser_dist_72a114f9._.js",
  "static/chunks/6a801_6c76338e._.js",
  "static/chunks/6a801_mermaid_dist_chunks_mermaid_core_diagram-PSM6KHXK_mjs_fb6ca92c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/Desktop/out_web/mermaid_online_cc/node_modules/mermaid/dist/chunks/mermaid.core/diagram-PSM6KHXK.mjs [app-client] (ecmascript)");
    });
});
}),
]);