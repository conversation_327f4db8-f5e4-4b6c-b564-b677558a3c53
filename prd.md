好的，这是一个关于“mermaid 在线查看且可以编辑的web网站”的产品需求文档 (PRD)。

## 产品需求文档 (PRD)

**产品名称：** Mermaid 在线编辑器

**版本：** 1.0

**日期：** 2023年10月27日

**作者：** AI Assistant

---

### 1. 简介

本产品旨在提供一个简单、易用、无需登录和数据库的在线 Mermaid 图表编辑器。用户可以在浏览器中实时查看和编辑 Mermaid 语法，并即时渲染出对应的图表。此工具主要面向需要快速创建、共享和预览 Mermaid 图表的用户。

### 2. 产品目标

*   提供一个无需安装、开箱即用的 Mermaid 图表编辑环境。
*   实现 Mermaid 语法与图表渲染的实时同步。
*   支持图表的导出和分享。
*   简化用户操作，无需登录或数据存储。

### 3. 用户故事 (User Stories)

*   **作为一名开发者，我希望能够快速编写 Mermaid 语法并实时预览图表，以便验证我的图表是否正确。**
*   **作为一名产品经理，我希望能够轻松地创建流程图或时序图，并将其分享给团队成员，而不需要复杂的工具或注册。**
*   **作为一名学生，我希望能够在线学习和实验 Mermaid 语法，并看到它如何生成图表。**
*   **作为一名用户，我希望能够将我创建的 Mermaid 图表导出为图片或代码，以便在其他地方使用。**

### 4. 功能需求

#### 4.1 核心功能

1.  **代码编辑区：**
    *   提供一个文本编辑区域，供用户输入 Mermaid 语法。
    *   支持基本的文本编辑操作（剪切、复制、粘贴、撤销、重做）。
    *   可选的代码高亮显示，以提高可读性。
2.  **图表预览区：**
    *   实时渲染 Mermaid 语法生成的图表。
    *   当代码编辑区内容改变时，图表预览区应立即更新。
    *   支持图表的缩放和平移（如果图表过大）。
3.  **语法示例与模板：**
    *   提供一些常见的 Mermaid 图表类型（流程图、时序图、类图、甘特图等）的语法示例或模板，方便用户快速开始。
    *   点击示例/模板后，其语法内容自动填充到代码编辑区。
4.  **导出功能：**
    *   **导出为图片：** 允许用户将当前渲染的图表导出为常见的图片格式（如 PNG, SVG）。
    *   **导出为 Mermaid 代码：** 允许用户复制或下载当前编辑区中的 Mermaid 语法。

#### 4.2 辅助功能

1.  **分享功能（可选，但推荐）：**
    *   生成一个包含当前 Mermaid 语法的 URL，用户可以通过该 URL 分享图表，其他人打开链接后即可看到相同的代码和图表。
    *   **实现方式：** 将 Mermaid 代码进行 Base64 编码或压缩后，作为 URL 参数。
2.  **主题切换：**
    *   提供浅色和深色两种主题，用户可根据喜好切换。
3.  **错误提示：**
    *   当 Mermaid 语法有误时，在代码编辑区或图表预览区下方显示友好的错误提示信息。

### 5. 非功能需求

1.  **性能：**
    *   图表渲染应快速响应，避免卡顿。
    *   页面加载速度快。
2.  **可用性 (Usability)：**
    *   界面简洁直观，易于理解和操作。
    *   提供清晰的提示和引导。
3.  **兼容性 (Compatibility)：**
    *   支持主流现代浏览器（Chrome, Firefox, Edge, Safari）。
4.  **安全性 (Security)：**
    *   由于不涉及用户数据存储和登录，主要关注前端代码安全，防止 XSS 等攻击。
5.  **可维护性 (Maintainability)：**
    *   代码结构清晰，易于维护和扩展。

### 6. 技术选型 (推荐)

*   **前端框架：** next.js
*   **Mermaid 渲染库：** `mermaid.js` 官方库
*   **代码编辑器：** CodeMirror / Monaco Editor (或简单的 `<textarea>`)
*   **样式库：** Tailwind CSS / Bootstrap (或纯 CSS)
*   **图片导出：** `html2canvas` 或 `dom-to-image` (用于将 SVG 转换为 Canvas 再导出 PNG)
*   **SVG 导出：** 直接获取 Mermaid 渲染的 SVG 内容。

### 7. 界面设计草图 (Wireframe)

下面是一个简单的界面布局草图，大致分为左右两栏：

```
+-----------------------------------------------------+
|                  Mermaid 在线编辑器                 |
+-----------------------------------------------------+
|                                                     |
|  +---------------------------+  +------------------+  |
|  |   代码编辑区              |  |   图表预览区     |  |
|  | (textarea 或代码编辑器)   |  | (mermaid 渲染结果)|  |
|  |                           |  |                  |  |
|  |   1. 流程图示例           |  |                  |  |
|  |   2. 时序图示例           |  |                  |  |
|  |   ...                     |  |                  |  |
|  |                           |  |                  |  |
|  |                           |  |                  |  |
|  |                           |  |                  |  |
|  |                           |  |                  |  |
|  +---------------------------+  +------------------+  |
|                                                     |
|  +-------------------------------------------------+  |
|  | [导出为图片] [导出为代码] [分享链接] [主题切换]  |  |
|  +-------------------------------------------------+  |
|                                                     |
+-----------------------------------------------------+
```
