'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { useTheme } from 'next-themes';
import { initializeMermaid, renderMermaid } from '../lib/mermaid-config';
import { debounce } from '../lib/utils';

interface PreviewProps {
  code: string;
  onRenderComplete?: (svg: string) => void;
  onRenderError?: (error: string) => void;
}

export default function Preview({ code, onRenderComplete, onRenderError }: PreviewProps) {
  const { theme } = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRenderedCode, setLastRenderedCode] = useState<string>('');

  // 防抖渲染函数
  const debouncedRender = useCallback(
    debounce(async (codeToRender: string) => {
      if (!containerRef.current || !codeToRender.trim()) {
        return;
      }

      // 如果代码没有变化，跳过渲染
      if (codeToRender === lastRenderedCode) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // 初始化 Mermaid
        initializeMermaid(theme === 'dark');

        // 生成唯一ID
        const diagramId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // 渲染图表
        const { svg } = await renderMermaid(containerRef.current, codeToRender, diagramId);
        
        setLastRenderedCode(codeToRender);
        onRenderComplete?.(svg);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '渲染失败';
        setError(errorMessage);
        onRenderError?.(errorMessage);
        
        // 显示错误信息
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="flex items-center justify-center h-full">
              <div class="text-center p-6 max-w-md">
                <div class="text-red-500 mb-4">
                  <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">图表渲染错误</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">${errorMessage}</p>
                <div class="text-xs text-gray-500 dark:text-gray-500">
                  请检查 Mermaid 语法是否正确
                </div>
              </div>
            </div>
          `;
        }
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [theme, lastRenderedCode, onRenderComplete, onRenderError]
  );

  // 当代码或主题变化时重新渲染
  useEffect(() => {
    if (code.trim()) {
      debouncedRender(code);
    } else {
      // 清空预览区域
      if (containerRef.current) {
        containerRef.current.innerHTML = `
          <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div class="text-center">
              <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="text-lg font-medium">开始编写 Mermaid 代码</p>
              <p class="text-sm opacity-75 mt-1">图表将在这里实时显示</p>
            </div>
          </div>
        `;
      }
      setLastRenderedCode('');
    }
  }, [code, debouncedRender]);

  // 主题变化时重新渲染
  useEffect(() => {
    if (lastRenderedCode) {
      setLastRenderedCode(''); // 强制重新渲染
      debouncedRender(code);
    }
  }, [theme]);

  return (
    <div className="h-full border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* 加载指示器 */}
      {isLoading && (
        <div className="absolute top-4 right-4 z-10">
          <div className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-3 py-2 rounded-lg shadow-sm">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span className="text-sm font-medium">渲染中...</span>
          </div>
        </div>
      )}

      {/* 预览容器 */}
      <div 
        ref={containerRef}
        className="h-full w-full p-4 overflow-auto"
        style={{
          // 确保 SVG 能够正确显示
          minHeight: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />

      {/* 缩放控制 */}
      <div className="absolute bottom-4 right-4 flex space-x-2">
        <button
          onClick={() => {
            const container = containerRef.current;
            if (container) {
              const svg = container.querySelector('svg');
              if (svg) {
                const currentScale = parseFloat(svg.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || '1');
                const newScale = Math.max(0.1, currentScale - 0.1);
                svg.style.transform = `scale(${newScale})`;
                svg.style.transformOrigin = 'center';
              }
            }
          }}
          className="p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          title="缩小"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        </button>
        
        <button
          onClick={() => {
            const container = containerRef.current;
            if (container) {
              const svg = container.querySelector('svg');
              if (svg) {
                svg.style.transform = 'scale(1)';
                svg.style.transformOrigin = 'center';
              }
            }
          }}
          className="p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          title="重置缩放"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
        
        <button
          onClick={() => {
            const container = containerRef.current;
            if (container) {
              const svg = container.querySelector('svg');
              if (svg) {
                const currentScale = parseFloat(svg.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || '1');
                const newScale = Math.min(3, currentScale + 0.1);
                svg.style.transform = `scale(${newScale})`;
                svg.style.transformOrigin = 'center';
              }
            }
          }}
          className="p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          title="放大"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>
    </div>
  );
}
