'use client';

import { useState } from 'react';
import { mermaidExamples, getExamplesByCategory, type MermaidExample } from '../lib/examples';

interface ExamplesProps {
  onSelectExample: (code: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export default function Examples({ onSelectExample, isOpen, onToggle }: ExamplesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('流程图');
  const examplesByCategory = getExamplesByCategory();
  const categories = Object.keys(examplesByCategory);

  const handleExampleClick = (example: MermaidExample) => {
    onSelectExample(example.code);
    // 在移动端选择示例后自动关闭侧边栏
    if (window.innerWidth < 768) {
      onToggle();
    }
  };

  return (
    <>
      {/* 移动端遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onToggle}
        />
      )}

      {/* 侧边栏 */}
      <div className={`
        fixed md:relative top-0 left-0 h-full w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
      `}>
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            示例模板
          </h2>
          <button
            onClick={onToggle}
            className="md:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 分类标签 */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`
                  px-3 py-1 text-sm rounded-full transition-colors
                  ${selectedCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                  }
                `}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* 示例列表 */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 space-y-3">
            {examplesByCategory[selectedCategory]?.map((example) => (
              <div
                key={example.id}
                onClick={() => handleExampleClick(example)}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                    {example.title}
                  </h3>
                  <svg className="w-4 h-4 text-gray-400 group-hover:text-blue-500 flex-shrink-0 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {example.description}
                </p>
                <div className="bg-gray-50 dark:bg-gray-800 rounded p-3">
                  <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-hidden">
                    <code className="line-clamp-4">
                      {example.code.split('\n').slice(0, 4).join('\n')}
                      {example.code.split('\n').length > 4 && '\n...'}
                    </code>
                  </pre>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部提示 */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            点击示例可快速加载到编辑器
          </div>
        </div>
      </div>

      {/* 切换按钮（移动端） */}
      <button
        onClick={onToggle}
        className={`
          fixed top-20 left-4 z-30 p-3 bg-blue-500 text-white rounded-full shadow-lg md:hidden
          transform transition-all duration-300
          ${isOpen ? 'translate-x-80' : 'translate-x-0'}
        `}
        title="示例模板"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </>
  );
}

// 添加 CSS 类用于文本截断
const styles = `
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

// 在组件挂载时添加样式
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
