# Mermaid 在线编辑器

一个基于 Next.js 的在线 Mermaid 图表编辑器，支持实时预览、代码编辑、图表导出和分享功能。

## 功能特性

### 核心功能
- 🎨 **实时预览** - 编辑代码时图表实时更新
- 💻 **代码编辑器** - 基于 Monaco Editor，支持语法高亮和自动补全
- 📊 **多种图表类型** - 支持流程图、时序图、类图、甘特图、饼图等
- 🎯 **示例模板** - 提供丰富的示例代码，快速上手

### 导出功能
- 🖼️ **图片导出** - 支持导出为 PNG 和 SVG 格式
- 📄 **代码导出** - 支持复制代码到剪贴板或下载为文件
- 🔗 **分享链接** - 生成包含代码的分享链接

### 用户体验
- 🌓 **主题切换** - 支持亮色和暗色主题
- 📱 **响应式设计** - 适配桌面端和移动端
- 💾 **本地存储** - 自动保存最后编辑的代码
- 🔍 **图表缩放** - 支持图表的缩放和平移

## 技术栈

- **前端框架**: Next.js 15 (App Router)
- **UI 样式**: Tailwind CSS
- **代码编辑器**: Monaco Editor
- **图表渲染**: Mermaid.js
- **主题管理**: next-themes
- **图片导出**: html2canvas, dom-to-image
- **代码压缩**: lz-string

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 项目结构

```
mermaid_online_cc/
├── app/                    # Next.js App Router 页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页面
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── Editor.tsx         # 代码编辑器组件
│   ├── Preview.tsx        # 图表预览组件
│   ├── Toolbar.tsx        # 工具栏组件
│   ├── Examples.tsx       # 示例模板组件
│   └── ThemeProvider.tsx  # 主题提供者组件
├── lib/                   # 工具库
│   ├── examples.ts        # 示例数据
│   ├── mermaid-config.ts  # Mermaid 配置
│   └── utils.ts           # 工具函数
└── public/               # 静态资源
```

## 支持的图表类型

- **流程图** (Flowchart) - 展示流程和决策路径
- **时序图** (Sequence Diagram) - 展示对象间的交互时序
- **类图** (Class Diagram) - 展示类之间的关系
- **甘特图** (Gantt Chart) - 展示项目进度和时间安排
- **饼图** (Pie Chart) - 展示数据分布
- **状态图** (State Diagram) - 展示状态转换
- **ER图** (Entity Relationship Diagram) - 展示数据库实体关系
- **用户旅程图** (User Journey) - 展示用户体验流程

## 使用说明

1. **编辑代码** - 在左侧编辑器中输入 Mermaid 代码
2. **实时预览** - 右侧会实时显示图表预览
3. **选择示例** - 点击示例模板快速加载预设代码
4. **导出图表** - 使用工具栏导出功能保存图表
5. **分享链接** - 生成包含代码的分享链接

## 部署

### Vercel 部署

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/mermaid-online-editor)

### 其他平台

项目支持部署到任何支持 Next.js 的平台，如 Netlify、Railway 等。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

MIT License
